// ===== ORDERS MANAGEMENT SCRIPT =====
console.log('🛒 Loading orders management script...');

// Global variables
let allOrders = [];
let filteredOrders = [];
let currentPage = 1;
let itemsPerPage = 10;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🛒 Orders page DOM loaded');
    
    // Load orders from data manager
    loadOrders();
    
    // Initialize event listeners
    initializeEventListeners();
    
    // Initial render
    renderOrders();
    updatePagination();
});

// Load orders from global data manager
function loadOrders() {
    console.log('📊 Loading orders from data manager...');
    
    // Get orders from global data manager
    if (window.RaoufStoreData && window.RaoufStoreData.orders) {
        allOrders = [...window.RaoufStoreData.orders];
        console.log('📊 Loaded orders from data manager:', allOrders.length);
    } else if (window.getAllOrders) {
        allOrders = window.getAllOrders();
        console.log('📊 Loaded orders from function:', allOrders.length);
    } else {
        // Fallback to sample data
        allOrders = getSampleOrders();
        console.log('📊 Using sample orders:', allOrders.length);
    }
    
    filteredOrders = [...allOrders];
    console.log('🛒 Orders loaded successfully');
}

// Get sample orders for demo
function getSampleOrders() {
    return [
        {
            id: 1001,
            user_id: 2,
            customer_name: 'John Doe',
            customer_email: '<EMAIL>',
            date: new Date().toISOString().split('T')[0],
            total: 599.99,
            status: 'processing',
            payment_status: 'paid',
            shipping_address: '456 Main St, Anytown, AT 67890',
            items: [
                { product_name: 'iPhone 13', quantity: 1, price: 599.99, subtotal: 599.99 }
            ]
        },
        {
            id: 1002,
            user_id: 2,
            customer_name: 'John Doe',
            customer_email: '<EMAIL>',
            date: new Date(Date.now() - 86400000).toISOString().split('T')[0],
            total: 349.99,
            status: 'shipped',
            payment_status: 'paid',
            shipping_address: '456 Main St, Anytown, AT 67890',
            items: [
                { product_name: 'Samsung Galaxy A54', quantity: 1, price: 349.99, subtotal: 349.99 }
            ]
        },
        {
            id: 1003,
            user_id: 3,
            customer_name: 'Jane Doe',
            customer_email: '<EMAIL>',
            date: new Date(Date.now() - 172800000).toISOString().split('T')[0],
            total: 899.99,
            status: 'delivered',
            payment_status: 'paid',
            shipping_address: '789 Oak St, Springfield, SP 54321',
            items: [
                { product_name: 'iPhone 14 Pro', quantity: 1, price: 899.99, subtotal: 899.99 }
            ]
        }
    ];
}

// Initialize event listeners
function initializeEventListeners() {
    console.log('🎯 Initializing orders event listeners...');
    
    // Search functionality
    const searchInput = document.getElementById('order-search');
    if (searchInput) {
        searchInput.addEventListener('input', handleSearch);
    }
    
    // Status filter
    const statusFilter = document.getElementById('status-filter');
    if (statusFilter) {
        statusFilter.addEventListener('change', handleStatusFilter);
    }
    
    // Pagination
    const prevBtn = document.getElementById('prev-page');
    const nextBtn = document.getElementById('next-page');
    
    if (prevBtn) {
        prevBtn.addEventListener('click', () => changePage(currentPage - 1));
    }
    
    if (nextBtn) {
        nextBtn.addEventListener('click', () => changePage(currentPage + 1));
    }
    
    // Modal close
    const modal = document.getElementById('order-modal');
    const closeBtn = modal?.querySelector('.close-btn');
    
    if (closeBtn) {
        closeBtn.addEventListener('click', closeModal);
    }
    
    if (modal) {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeModal();
            }
        });
    }
    
    // Status update form
    const statusForm = document.getElementById('update-status-form');
    if (statusForm) {
        statusForm.addEventListener('submit', handleStatusUpdate);
    }
    
    console.log('✅ Event listeners initialized');
}

// Render orders table
function renderOrders() {
    console.log('📊 Rendering orders...');
    const tableBody = document.getElementById('orders-table-body');
    
    if (!tableBody) {
        console.error('❌ Orders table body not found');
        return;
    }
    
    // Clear existing content
    tableBody.innerHTML = '';
    
    if (!filteredOrders || filteredOrders.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="7" style="text-align: center; color: #666; padding: 40px;">No orders found</td>';
        tableBody.appendChild(row);
        console.log('⚠️ No orders to display');
        return;
    }
    
    // Calculate page slice
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const ordersToShow = filteredOrders.slice(startIndex, endIndex);
    
    ordersToShow.forEach((order, index) => {
        const row = document.createElement('tr');
        
        row.innerHTML = `
            <td>#${order.id}</td>
            <td>
                <strong>${order.customer_name}</strong><br>
                <small style="color: #666;">${order.customer_email}</small>
            </td>
            <td>${formatDate(order.date)}</td>
            <td>$${order.total.toFixed(2)}</td>
            <td>
                <span class="status-badge status-${order.status}">
                    ${order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                </span>
            </td>
            <td>
                <span class="status-badge ${order.payment_status === 'paid' ? 'status-delivered' : 'status-pending'}">
                    ${order.payment_status.charAt(0).toUpperCase() + order.payment_status.slice(1)}
                </span>
            </td>
            <td>
                <button class="btn small-btn edit-btn" onclick="viewOrderDetails(${order.id})" title="View details">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn small-btn delete-btn" onclick="deleteOrder(${order.id})" title="Delete order">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        
        tableBody.appendChild(row);
        console.log(`✅ Added order ${index + 1}: #${order.id}`);
    });
    
    console.log('🎯 Orders rendered successfully');
}

// Handle search
function handleSearch() {
    const searchTerm = document.getElementById('order-search').value.toLowerCase();
    
    if (searchTerm.trim() === '') {
        filteredOrders = [...allOrders];
    } else {
        filteredOrders = allOrders.filter(order => 
            order.id.toString().includes(searchTerm) ||
            order.customer_name.toLowerCase().includes(searchTerm) ||
            order.customer_email.toLowerCase().includes(searchTerm)
        );
    }
    
    currentPage = 1;
    renderOrders();
    updatePagination();
}

// Handle status filter
function handleStatusFilter() {
    const status = document.getElementById('status-filter').value;
    
    if (status === '') {
        filteredOrders = [...allOrders];
    } else {
        filteredOrders = allOrders.filter(order => order.status === status);
    }
    
    currentPage = 1;
    renderOrders();
    updatePagination();
}

// Update pagination
function updatePagination() {
    const totalPages = Math.ceil(filteredOrders.length / itemsPerPage);
    const pageInfo = document.getElementById('page-info');
    const prevBtn = document.getElementById('prev-page');
    const nextBtn = document.getElementById('next-page');
    
    if (pageInfo) {
        pageInfo.textContent = `Page ${currentPage} of ${totalPages || 1}`;
    }
    
    if (prevBtn) {
        prevBtn.disabled = currentPage <= 1;
    }
    
    if (nextBtn) {
        nextBtn.disabled = currentPage >= totalPages;
    }
}

// Change page
function changePage(page) {
    const totalPages = Math.ceil(filteredOrders.length / itemsPerPage);
    
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        renderOrders();
        updatePagination();
    }
}

// View order details
function viewOrderDetails(orderId) {
    const order = allOrders.find(o => o.id === orderId);
    if (!order) {
        console.error('Order not found:', orderId);
        return;
    }
    
    console.log('👁️ Viewing order details:', orderId);
    
    // Populate modal
    document.getElementById('detail-order-id').textContent = `#${order.id}`;
    document.getElementById('detail-date').textContent = formatDate(order.date);
    document.getElementById('detail-status').textContent = order.status.charAt(0).toUpperCase() + order.status.slice(1);
    document.getElementById('detail-payment').textContent = order.payment_status.charAt(0).toUpperCase() + order.payment_status.slice(1);
    document.getElementById('detail-customer-name').textContent = order.customer_name;
    document.getElementById('detail-customer-email').textContent = order.customer_email;
    document.getElementById('detail-shipping-address').textContent = order.shipping_address;
    document.getElementById('detail-total').textContent = `$${order.total.toFixed(2)}`;
    
    // Populate items
    const itemsBody = document.getElementById('order-items-body');
    itemsBody.innerHTML = '';
    
    if (order.items && order.items.length > 0) {
        order.items.forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.product_name}</td>
                <td>${item.quantity}</td>
                <td>$${item.price.toFixed(2)}</td>
                <td>$${item.subtotal.toFixed(2)}</td>
            `;
            itemsBody.appendChild(row);
        });
    }
    
    // Set current status in select
    const statusSelect = document.getElementById('status-select');
    if (statusSelect) {
        statusSelect.value = order.status;
        statusSelect.setAttribute('data-order-id', order.id);
    }
    
    // Show modal
    const modal = document.getElementById('order-modal');
    if (modal) {
        modal.style.display = 'flex';
    }
}

// Close modal
function closeModal() {
    const modal = document.getElementById('order-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// Handle status update
function handleStatusUpdate(e) {
    e.preventDefault();
    
    const statusSelect = document.getElementById('status-select');
    const orderId = parseInt(statusSelect.getAttribute('data-order-id'));
    const newStatus = statusSelect.value;
    
    // Update order status
    const orderIndex = allOrders.findIndex(o => o.id === orderId);
    if (orderIndex !== -1) {
        allOrders[orderIndex].status = newStatus;
        
        // Update in data manager if available
        if (window.RaoufStoreData && window.RaoufStoreData.orders) {
            const globalIndex = window.RaoufStoreData.orders.findIndex(o => o.id === orderId);
            if (globalIndex !== -1) {
                window.RaoufStoreData.orders[globalIndex].status = newStatus;
                
                // Save to storage
                if (window.saveDataToStorage) {
                    window.saveDataToStorage();
                }
            }
        }
        
        // Refresh display
        filteredOrders = [...allOrders];
        renderOrders();
        closeModal();
        
        // Show notification
        showNotification(`Order #${orderId} status updated to ${newStatus}`);
        
        console.log(`✅ Order #${orderId} status updated to ${newStatus}`);
    }
}

// Delete order
function deleteOrder(orderId) {
    if (confirm(`Are you sure you want to delete order #${orderId}?`)) {
        const orderIndex = allOrders.findIndex(o => o.id === orderId);
        if (orderIndex !== -1) {
            allOrders.splice(orderIndex, 1);
            
            // Update in data manager if available
            if (window.RaoufStoreData && window.RaoufStoreData.orders) {
                const globalIndex = window.RaoufStoreData.orders.findIndex(o => o.id === orderId);
                if (globalIndex !== -1) {
                    window.RaoufStoreData.orders.splice(globalIndex, 1);
                    
                    // Save to storage
                    if (window.saveDataToStorage) {
                        window.saveDataToStorage();
                    }
                }
            }
            
            // Refresh display
            filteredOrders = [...allOrders];
            renderOrders();
            updatePagination();
            
            showNotification(`Order #${orderId} deleted successfully`);
            console.log(`🗑️ Order #${orderId} deleted`);
        }
    }
}

// Format date
function formatDate(dateString) {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-US', options);
}

// Show notification
function showNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'notification success';
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #d4edda;
        color: #155724;
        padding: 15px 20px;
        border-radius: 4px;
        border-left: 4px solid #28a745;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        animation: slideInRight 0.3s ease-out;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
    
    console.log('🔔 Notification:', message);
}

console.log('🛒 Orders management script loaded successfully');

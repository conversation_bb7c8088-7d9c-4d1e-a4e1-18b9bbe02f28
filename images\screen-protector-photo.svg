<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="350" height="450" viewBox="0 0 350 450">
  <!-- Screen Protector Background -->
  <defs>
    <linearGradient id="glass-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:0.5" />
      <stop offset="50%" style="stop-color:#E0F7FA;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#B2EBF2;stop-opacity:0.4" />
    </linearGradient>
    <filter id="glass-shine" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" />
      <feColorMatrix type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 0.3 0" />
    </filter>
    <linearGradient id="edge-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#EEEEEE;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#EEEEEE;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Package Background -->
  <rect x="50" y="50" width="250" height="350" rx="5" ry="5" fill="#ECEFF1" />
  
  <!-- Package Details -->
  <rect x="50" y="50" width="250" height="40" rx="5" ry="5" fill="#CFD8DC" />
  <text x="175" y="75" font-family="Arial" font-size="14" font-weight="bold" text-anchor="middle" fill="#37474F">Premium Tempered Glass</text>
  
  <!-- Screen Protector Image -->
  <rect x="75" y="110" width="200" height="230" rx="10" ry="10" fill="url(#glass-gradient)" />
  
  <!-- Glass Reflections -->
  <path d="M75,110 L275,110 L275,340 L75,340 Z" stroke="#FFFFFF" stroke-width="0.5" fill="none" />
  <path d="M95,130 L255,130 L255,320 L95,320 Z" stroke="#FFFFFF" stroke-width="0.5" fill="none" />
  
  <!-- Glass Edge -->
  <rect x="75" y="110" width="200" height="5" fill="url(#edge-gradient)" />
  <rect x="75" y="335" width="200" height="5" fill="url(#edge-gradient)" />
  <rect x="75" y="110" width="5" height="230" fill="url(#edge-gradient)" />
  <rect x="270" y="110" width="5" height="230" fill="url(#edge-gradient)" />
  
  <!-- Light Reflections -->
  <ellipse cx="175" cy="200" rx="80" ry="40" fill="#FFFFFF" opacity="0.15" filter="url(#glass-shine)" />
  <ellipse cx="140" cy="150" rx="40" ry="20" fill="#FFFFFF" opacity="0.2" filter="url(#glass-shine)" />
  <path d="M100,150 L110,140 L250,280 L240,290 Z" fill="#FFFFFF" opacity="0.15" />
  
  <!-- Cutouts for front camera, home button, etc. -->
  <circle cx="175" cy="130" r="10" fill="#ECEFF1" />
  <rect x="155" cy="320" width="40" height="10" rx="5" ry="5" fill="#ECEFF1" />
  
  <!-- Product Features -->
  <text x="90" y="360" font-family="Arial" font-size="10" text-anchor="start" fill="#546E7A">• 9H Hardness</text>
  <text x="90" y="380" font-family="Arial" font-size="10" text-anchor="start" fill="#546E7A">• Oleophobic Coating</text>
  <text x="200" y="360" font-family="Arial" font-size="10" text-anchor="start" fill="#546E7A">• Anti-Fingerprint</text>
  <text x="200" y="380" font-family="Arial" font-size="10" text-anchor="start" fill="#546E7A">• Easy Installation</text>
</svg> 
// Constants
const API_URL = 'http://localhost:3000/api';
let cart = [];

// DOM Elements
document.addEventListener('DOMContentLoaded', () => {
    // Global variables
    window.API_URL = 'http://localhost:3000/api';
    
    // Load cart from localStorage
    loadCart();
    
    // Update cart count
    updateCartCount();
    
    // Notification system
    window.showNotification = function(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <span>${message}</span>
            <button class="close-notification"><i class="fas fa-times"></i></button>
        `;
        
        document.body.appendChild(notification);
        
        // Show notification with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // Auto-hide after 5 seconds
        const hideTimeout = setTimeout(() => {
            hideNotification(notification);
        }, 5000);
        
        // Close button
        const closeBtn = notification.querySelector('.close-notification');
        closeBtn.addEventListener('click', () => {
            clearTimeout(hideTimeout);
            hideNotification(notification);
        });
    };
    
    function hideNotification(notification) {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }
    
    // Function to create a product card
    window.createProductCard = function(product) {
        const productCard = document.createElement('div');
        productCard.className = 'product-card';
        
        // Map product names to real product images
        let imageUrl = 'images/real-products/iphone13_main.jpg'; // Default fallback
        
        // Map product names to corresponding real images
        if (product.name.toLowerCase().includes('iphone')) {
            imageUrl = 'images/real-products/iphone13_main.jpg';
        } else if (product.name.toLowerCase().includes('samsung') || product.name.toLowerCase().includes('galaxy')) {
            imageUrl = 'images/real-products/samsung_s21_main.jpg';
        } else if (product.name.toLowerCase().includes('pixel')) {
            imageUrl = 'images/real-products/pixel6_main.jpg';
        } else if (product.name.toLowerCase().includes('oneplus')) {
            imageUrl = 'images/real-products/oneplus9_main.jpg';
        } else if (product.name.toLowerCase().includes('xiaomi')) {
            imageUrl = 'images/real-products/xiaomi12_main.jpg';
        } else if (product.name.toLowerCase().includes('ipad')) {
            imageUrl = 'images/real-products/ipad_pro_main.jpg';
        } else if (product.name.toLowerCase().includes('tab')) {
            imageUrl = 'images/real-products/galaxy_tab_main.jpg';
        } else if (product.name.toLowerCase().includes('case')) {
            imageUrl = 'images/real-products/premium_case_main.jpg';
        } else if (product.name.toLowerCase().includes('charger')) {
            imageUrl = 'images/real-products/fast_charger_main.jpg';
        } else if (product.name.toLowerCase().includes('screen')) {
            imageUrl = 'images/real-products/screen_protector_main.jpg';
        }
        
        // Override with product's image if it exists
        if (product.images && product.images.length > 0 && product.images[0].image_url) {
            // Only override if the image is from real-products directory
            if (product.images[0].image_url.includes('real-products')) {
                imageUrl = product.images[0].image_url;
            }
        }
        
        // Generate random rating for demo purposes
        const rating = ((Math.random() * 2) + 3).toFixed(1); // Random rating between 3.0 and 5.0
        
        productCard.innerHTML = `
            <div class="product-image">
                <img src="${imageUrl}" alt="${product.name}" onerror="this.src='images/real-products/iphone13_main.jpg'">
            </div>
            <div class="product-info">
                <h3>${product.name}</h3>
                <div class="product-price">$${parseFloat(product.price).toFixed(2)}</div>
                <div class="product-rating">
                    <span class="stars">
                        ${generateStars(rating)}
                    </span>
                    <span class="rating-count">(${Math.floor(Math.random() * 100) + 1})</span>
                </div>
            </div>
            <div class="product-actions">
                <a href="product-details.html?id=${product.product_id}" class="btn secondary-btn">View Details</a>
                <button class="btn primary-btn add-to-cart-btn" data-id="${product.product_id}" data-name="${product.name}" data-price="${product.price}" data-image="${imageUrl}">
                    <i class="fas fa-cart-plus"></i>
                </button>
            </div>
        `;
        
        return productCard;
    };
    
    // Generate star rating HTML
    function generateStars(rating) {
        const fullStars = Math.floor(rating);
        const halfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (halfStar ? 1 : 0);
        
        let starsHTML = '';
        
        // Add full stars
        for (let i = 0; i < fullStars; i++) {
            starsHTML += '<i class="fas fa-star"></i>';
        }
        
        // Add half star if needed
        if (halfStar) {
            starsHTML += '<i class="fas fa-star-half-alt"></i>';
        }
        
        // Add empty stars
        for (let i = 0; i < emptyStars; i++) {
            starsHTML += '<i class="far fa-star"></i>';
        }
        
        return starsHTML;
    }
    
    // Handle adding products to cart
    window.handleAddToCart = function(event) {
        const button = event.target.closest('.add-to-cart-btn');
        if (!button) return;

        // Check if user is authenticated
        if (!window.authManager || !window.authManager.isAuthenticated()) {
            const productName = button.dataset.name || 'this product';
            window.authManager?.showAuthModal(
                `Please login or register to add ${productName} to your cart.`,
                {
                    type: 'addToCart',
                    data: {
                        id: button.dataset.id,
                        name: button.dataset.name,
                        price: button.dataset.price,
                        image: button.dataset.image
                    }
                }
            );
            return;
        }

        // Prevent double execution
        if (button.dataset.processing === 'true') {
            return;
        }
        button.dataset.processing = 'true';

        const productId = button.dataset.id;
        const productName = button.dataset.name;
        const productPrice = parseFloat(button.dataset.price);
        const productImage = button.dataset.image;
        
        // Get current cart (user-specific)
        let cart = [];
        const userId = window.authManager?.getCurrentUser()?.user_id || 'guest';
        const cartKey = `cart_${userId}`;
        const savedCart = localStorage.getItem(cartKey);
        if (savedCart) {
            try {
                cart = JSON.parse(savedCart);
            } catch (e) {
                console.error('Error parsing cart from localStorage:', e);
                cart = [];
            }
        }
        
        // Check if product is already in cart
        const existingItemIndex = cart.findIndex(item => item.id === productId);
        
        if (existingItemIndex >= 0) {
            // Update quantity if already in cart
            cart[existingItemIndex].quantity += 1;
        } else {
            // Add new item to cart
            cart.push({
                id: productId,
                name: productName,
                price: productPrice,
                image: productImage,
                quantity: 1
            });
        }
        
        // Save cart to localStorage (user-specific)
        localStorage.setItem(cartKey, JSON.stringify(cart));
        
        // Update cart count in header
        updateCartCount();
        
        // Show success message
        showNotification('Product added to cart!');

        // Reset processing flag
        setTimeout(() => {
            button.dataset.processing = 'false';
        }, 100);
    };
    
    // Add event listeners to any "Add to Cart" buttons on the page
    document.addEventListener('click', event => {
        const addToCartBtn = event.target.closest('.add-to-cart-btn');
        if (addToCartBtn) {
            event.preventDefault();
            event.stopPropagation();
            handleAddToCart(event);
        }
    });
    
    // Load featured products
    loadFeaturedProducts();

    // Handle pending actions after login
    window.addEventListener('executePendingAddToCart', (event) => {
        const productData = event.detail;
        if (productData) {
            // Simulate button click with the stored data
            const fakeButton = {
                dataset: productData,
                closest: () => ({ dataset: productData })
            };

            // Execute the add to cart action
            const fakeEvent = { target: fakeButton };
            handleAddToCart(fakeEvent);
        }
    });
});

// Load cart from localStorage (user-specific)
function loadCart() {
    const userId = window.authManager?.getCurrentUser()?.user_id || 'guest';
    const cartKey = `cart_${userId}`;
    const savedCart = localStorage.getItem(cartKey);
    if (savedCart) {
        try {
            cart = JSON.parse(savedCart);
        } catch (e) {
            console.error('Error parsing cart from localStorage:', e);
            cart = [];
        }
    }
}

// Save cart to localStorage (user-specific)
function saveCart() {
    const userId = window.authManager?.getCurrentUser()?.user_id || 'guest';
    const cartKey = `cart_${userId}`;
    localStorage.setItem(cartKey, JSON.stringify(cart));
}

// Update cart count in the UI (user-specific)
function updateCartCount() {
    const cartCountElements = document.querySelectorAll('.cart-count');
    if (cartCountElements.length === 0) return;

    let count = 0;

    // Only show cart count if user is authenticated
    if (window.authManager?.isAuthenticated()) {
        const userId = window.authManager.getCurrentUser()?.user_id || 'guest';
        const cartKey = `cart_${userId}`;
        const savedCart = localStorage.getItem(cartKey);

        if (savedCart) {
            try {
                const cart = JSON.parse(savedCart);
                count = cart.reduce((total, item) => total + item.quantity, 0);
            } catch (e) {
                console.error('Error parsing cart from localStorage:', e);
            }
        }
    }

    cartCountElements.forEach(element => {
        element.textContent = count;
    });
}

// Load featured products from API
async function loadFeaturedProducts() {
    const featuredProductsContainer = document.getElementById('featured-products');
    if (!featuredProductsContainer) return;
    
    try {
        // In a real application, we would fetch from API:
        // const response = await fetch(`${API_URL}/products/featured`);
        // const data = await response.json();
        
        // For demo purposes, we'll use sample data
        const products = [
            {
                product_id: 1,
                name: 'iPhone 13 Pro Max',
                price: 1099.99,
                description: 'Apple\'s flagship smartphone with a 6.7-inch display'
            },
            {
                product_id: 2,
                name: 'Samsung Galaxy S21',
                price: 799.99,
                description: 'Samsung\'s flagship smartphone with a 6.2-inch display'
            },
            {
                product_id: 3,
                name: 'Google Pixel 6',
                price: 699.00,
                description: 'Google\'s flagship smartphone with a 6.4-inch display'
            },
            {
                product_id: 4,
                name: 'OnePlus 9 Pro',
                price: 969.00,
                description: 'OnePlus flagship smartphone with a 6.7-inch display'
            }
        ];
        
        // Remove skeleton loaders
        featuredProductsContainer.innerHTML = '';
        
        // Add products to the container
        products.forEach(product => {
            const productCard = createProductCard(product);
            featuredProductsContainer.appendChild(productCard);
        });
    } catch (error) {
        console.error('Error loading featured products:', error);
    }
} 
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RaoufStore Admin - Products</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1><i class="fas fa-mobile-alt"></i> RaoufStore</h1>
                <p>Admin Panel</p>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li class="active"><a href="products.html"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="categories.html"><i class="fas fa-list"></i> Categories</a></li>
                    <li><a href="brands.html"><i class="fas fa-tag"></i> Brands</a></li>
                    <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="users.html"><i class="fas fa-users"></i> Users</a></li>
                    <li><a href="promotions.html"><i class="fas fa-percent"></i> Promotions</a></li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </aside>
        
        <main class="content">
            <header class="content-header">
                <h1>Products</h1>
                <div class="admin-user">
                    <span>Admin User</span>
                    <img src="../images/real-products/iphone13_3.jpg" alt="Admin">
                </div>
            </header>
            
            <div class="content-actions">
                <div class="search-bar">
                    <input type="text" id="product-search" placeholder="Search products...">
                    <button id="search-btn"><i class="fas fa-search"></i></button>
                </div>
                <div class="filter-actions">
                    <select id="category-filter">
                        <option value="">All Categories</option>
                        <!-- Will be populated from API -->
                    </select>
                    <select id="brand-filter">
                        <option value="">All Brands</option>
                        <!-- Will be populated from API -->
                    </select>
                    <select id="status-filter">
                        <option value="">All Status</option>
                        <option value="in-stock">In Stock</option>
                        <option value="low-stock">Low Stock</option>
                        <option value="out-of-stock">Out of Stock</option>
                    </select>
                </div>
                <button id="add-product-btn" class="primary-btn"><i class="fas fa-plus"></i> Add Product</button>
            </div>
            
            <div class="data-table">
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Image</th>
                            <th>Name</th>
                            <th>Category</th>
                            <th>Brand</th>
                            <th>Price</th>
                            <th>Stock</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="products-tbody">
                        <!-- Products will be loaded here -->
                    </tbody>
                </table>
            </div>
            
            <div class="pagination">
                <button id="prev-page" class="secondary-btn"><i class="fas fa-chevron-left"></i> Previous</button>
                <div id="page-numbers">
                    <!-- Page numbers will be generated here -->
                </div>
                <button id="next-page" class="secondary-btn">Next <i class="fas fa-chevron-right"></i></button>
            </div>
        </main>
    </div>
    
    <!-- Product Modal -->
    <div id="product-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modal-title">Add New Product</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="product-form">
                    <input type="hidden" id="product-id">
                    
                    <div class="form-group">
                        <label for="product-name">Product Name</label>
                        <input type="text" id="product-name" required>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="product-category">Category</label>
                            <select id="product-category" required>
                                <!-- Will be populated from API -->
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="product-brand">Brand</label>
                            <select id="product-brand" required>
                                <!-- Will be populated from API -->
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="product-price">Price ($)</label>
                            <input type="number" id="product-price" min="0" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label for="product-stock">Stock Quantity</label>
                            <input type="number" id="product-stock" min="0" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="product-description">Description</label>
                        <textarea id="product-description" rows="4" required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="product-images">Images</label>
                        <div class="image-upload-container">
                            <div class="image-preview-container" id="image-previews">
                                <!-- Image previews will be added here -->
                            </div>
                            <button type="button" id="add-image-btn" class="secondary-btn"><i class="fas fa-plus"></i> Add Image</button>
                            <input type="file" id="image-upload" accept="image/*" multiple hidden>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Specifications</label>
                        <div id="specifications-container">
                            <!-- Spec fields will be added here -->
                            <div class="spec-row">
                                <input type="text" placeholder="Name (e.g. Display)">
                                <input type="text" placeholder="Value (e.g. 6.7-inch OLED)">
                                <button type="button" class="remove-spec-btn"><i class="fas fa-times"></i></button>
                            </div>
                        </div>
                        <button type="button" id="add-spec-btn" class="secondary-btn"><i class="fas fa-plus"></i> Add Specification</button>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" id="cancel-btn" class="secondary-btn">Cancel</button>
                        <button type="submit" id="save-product-btn" class="primary-btn">Save Product</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../js/data-manager.js"></script>
    <script src="js/admin-app.js"></script>
    <script src="js/admin.js"></script>
    <script src="js/login.js"></script>
    <script src="js/products.js"></script>
</body>
</html> 
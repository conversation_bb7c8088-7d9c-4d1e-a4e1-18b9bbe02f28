/* Product Details Page Styles */

/* Product Details Container */
.product-details-section {
    padding: 60px 0;
}

.product-details-container {
    margin-bottom: 40px;
}

.product-details, .product-loading {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    background-color: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Product Image Gallery */
.product-image-gallery {
    display: flex;
    flex-direction: column;
}

.product-main-image {
    height: 400px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    border: 1px solid #eee;
    border-radius: 8px;
    overflow: hidden;
}

.product-main-image img {
    max-height: 100%;
    max-width: 100%;
    object-fit: contain;
}

.product-thumbnails {
    display: flex;
    gap: 15px;
}

.thumbnail-item {
    width: 80px;
    height: 80px;
    border: 1px solid #eee;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    opacity: 0.6;
    transition: opacity 0.3s, border-color 0.3s;
}

.thumbnail-item:hover,
.thumbnail-item.active {
    opacity: 1;
    border-color: var(--primary-color);
}

.thumbnail-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Product Info */
.product-info {
    display: flex;
    flex-direction: column;
}

.product-info h1 {
    font-size: 28px;
    margin-bottom: 15px;
    color: var(--dark-color);
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.product-rating {
    display: flex;
    align-items: center;
}

.product-rating .stars {
    color: #f8ce0b;
    margin-right: 10px;
}

.product-brand {
    font-size: 14px;
    color: #666;
}

.product-brand span {
    font-weight: 500;
    color: var(--dark-color);
}

.product-price {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.original-price {
    font-size: 16px;
    color: #999;
    text-decoration: line-through;
    margin-left: 10px;
}

.discount-badge {
    background-color: var(--accent-color);
    color: white;
    font-size: 12px;
    padding: 3px 8px;
    border-radius: 4px;
    margin-left: 10px;
}

.product-description {
    margin-bottom: 20px;
    line-height: 1.6;
}

.product-stock {
    margin-bottom: 20px;
    font-size: 14px;
}

.in-stock {
    color: var(--success-color);
    font-weight: 500;
}

.out-of-stock {
    color: var(--error-color);
    font-weight: 500;
}

.product-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.quantity-selector {
    display: flex;
    align-items: center;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.quantity-btn {
    width: 40px;
    height: 40px;
    background-color: #f5f5f5;
    border: none;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s;
}

.quantity-btn:hover {
    background-color: #e5e5e5;
}

#product-quantity {
    width: 50px;
    height: 40px;
    border: none;
    text-align: center;
    font-size: 16px;
}

#add-to-cart {
    flex-grow: 1;
}

.wishlist-btn {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

/* Product Tabs */
.product-tabs {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 60px;
    overflow: hidden;
}

.tabs-header {
    display: flex;
    border-bottom: 1px solid #eee;
}

.tab-btn {
    padding: 15px 30px;
    background-color: transparent;
    border: none;
    cursor: pointer;
    font-weight: 500;
    color: #666;
    transition: all 0.3s;
    position: relative;
}

.tab-btn:hover {
    color: var(--primary-color);
}

.tab-btn.active {
    color: var(--primary-color);
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--primary-color);
}

.tabs-content {
    padding: 30px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Specifications Tab */
.specifications-table {
    width: 100%;
    border-collapse: collapse;
}

.specifications-table tr {
    border-bottom: 1px solid #eee;
}

.specifications-table tr:last-child {
    border-bottom: none;
}

.specifications-table td {
    padding: 12px 15px;
}

.specifications-table td:first-child {
    width: 30%;
    font-weight: 500;
    color: var(--dark-color);
}

/* Reviews Tab */
.reviews-summary {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    border-bottom: 1px solid #eee;
    padding-bottom: 30px;
}

.average-rating {
    text-align: center;
}

.big-rating {
    font-size: 48px;
    font-weight: 700;
    color: var(--dark-color);
    line-height: 1;
}

.rating-stars {
    color: #f8ce0b;
    font-size: 18px;
    margin: 10px 0;
}

.total-reviews {
    font-size: 14px;
    color: #666;
}

.rating-bars {
    flex-grow: 1;
    max-width: 400px;
    margin-left: 40px;
}

.rating-bar {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.rating-label {
    width: 50px;
    font-size: 14px;
    color: #666;
}

.rating-label i {
    color: #f8ce0b;
}

.bar-container {
    flex-grow: 1;
    height: 8px;
    background-color: #eee;
    border-radius: 4px;
    margin: 0 15px;
    overflow: hidden;
}

.bar {
    height: 100%;
    background-color: #f8ce0b;
    border-radius: 4px;
}

.rating-count {
    font-size: 14px;
    color: #666;
    width: 30px;
    text-align: right;
}

.reviews-list {
    margin-bottom: 30px;
}

.review-item {
    display: flex;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.review-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #eee;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
}

.review-avatar i {
    font-size: 30px;
    color: #999;
}

.review-content {
    flex-grow: 1;
}

.review-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.review-author {
    font-weight: 500;
}

.review-date {
    font-size: 14px;
    color: #999;
}

.review-rating {
    margin-bottom: 10px;
    color: #f8ce0b;
}

.review-text {
    line-height: 1.6;
}

.review-form-container {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
}

.review-form-container h3 {
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group textarea {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.rating-selector {
    font-size: 24px;
    color: #ddd;
    margin-bottom: 10px;
}

.rating-selector i {
    cursor: pointer;
    margin-right: 5px;
}

.rating-selector i:hover,
.rating-selector i.active {
    color: #f8ce0b;
}

/* Shipping Tab */
.shipping-info h3 {
    margin-bottom: 15px;
    color: var(--dark-color);
}

.shipping-info p {
    margin-bottom: 15px;
    line-height: 1.6;
}

.shipping-info ul {
    margin-bottom: 30px;
    padding-left: 20px;
    list-style-type: disc;
}

.shipping-info ul li {
    margin-bottom: 5px;
    line-height: 1.6;
}

/* Related Products */
.related-products .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
}

/* Skeleton loading */
.skeleton-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #eee;
    margin-right: 20px;
}

/* Responsive design */
@media (max-width: 992px) {
    .product-details, .product-loading {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .reviews-summary {
        flex-direction: column;
    }
    
    .rating-bars {
        margin-left: 0;
        margin-top: 20px;
        max-width: 100%;
    }
}

@media (max-width: 768px) {
    .tabs-header {
        flex-wrap: wrap;
    }
    
    .tab-btn {
        flex-grow: 1;
        padding: 15px;
        text-align: center;
    }
} 
<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="300" height="600" viewBox="0 0 300 600">
  <!-- Samsung Galaxy Background -->
  <defs>
    <linearGradient id="samsung-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1A237E;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#303F9F;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="screen-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#81D4FA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4FC3F7;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="5" />
      <feOffset dx="0" dy="0" />
      <feComposite in2="SourceGraphic" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0" />
      <feBlend in="SourceGraphic" in2="blurOut" mode="normal" />
    </filter>
  </defs>

  <!-- Phone Body -->
  <rect x="60" y="20" width="180" height="560" rx="30" ry="30" fill="url(#samsung-gradient)" filter="url(#shadow)" />
  
  <!-- Screen (slightly curved) -->
  <path d="M75,70 L230,70 C233,70 235,68 235,65 L235,495 C235,492 233,490 230,490 L75,490 C72,490 70,492 70,495 L70,65 C70,68 72,70 75,70 Z" fill="url(#screen-gradient)" />
  
  <!-- Front Camera -->
  <circle cx="150" cy="45" r="5" fill="#333" />
  
  <!-- Volume Buttons -->
  <rect x="50" y="120" width="5" height="40" rx="2" ry="2" fill="#0D47A1" />
  
  <!-- Power Button -->
  <rect x="245" y="150" width="5" height="40" rx="2" ry="2" fill="#0D47A1" />
  
  <!-- Samsung Interface -->
  <!-- Status Bar -->
  <rect x="75" y="75" width="155" height="20" fill="#FFF" opacity="0.9" />
  <circle cx="215" cy="85" r="5" fill="#333" />
  <rect x="190" y="82" width="10" height="6" rx="1" ry="1" fill="#333" />
  <rect x="170" y="82" width="10" height="6" rx="3" ry="3" fill="#333" />
  
  <!-- Home Screen with Mountain Wallpaper -->
  <linearGradient id="mountain-bg" x1="0%" y1="0%" x2="0%" y2="100%">
    <stop offset="0%" style="stop-color:#4DD0E1;stop-opacity:1" />
    <stop offset="100%" style="stop-color:#B2EBF2;stop-opacity:1" />
  </linearGradient>
  <rect x="75" y="95" width="155" height="335" fill="url(#mountain-bg)" />
  
  <!-- Mountains -->
  <path d="M75,430 L120,330 L140,370 L170,280 L225,430 Z" fill="#26A69A" />
  <path d="M75,430 L100,370 L120,390 L150,320 L180,360 L230,430 Z" fill="#00796B" />
  
  <!-- App Icons (4x5 grid) -->
  <g transform="translate(85, 130)">
    <!-- Row 1 -->
    <rect x="0" y="0" width="25" height="25" rx="6" ry="6" fill="#FF5722" />
    <rect x="35" y="0" width="25" height="25" rx="6" ry="6" fill="#4CAF50" />
    <rect x="70" y="0" width="25" height="25" rx="6" ry="6" fill="#2196F3" />
    <rect x="105" y="0" width="25" height="25" rx="6" ry="6" fill="#9C27B0" />
    
    <!-- Row 2 -->
    <rect x="0" y="35" width="25" height="25" rx="6" ry="6" fill="#FFC107" />
    <rect x="35" y="35" width="25" height="25" rx="6" ry="6" fill="#3F51B5" />
    <rect x="70" y="35" width="25" height="25" rx="6" ry="6" fill="#E91E63" />
    <rect x="105" y="35" width="25" height="25" rx="6" ry="6" fill="#009688" />
  </g>
  
  <!-- Bottom Navigation Bar -->
  <rect x="75" y="430" width="155" height="50" fill="#333" opacity="0.8" />
  <circle cx="105" cy="455" r="10" stroke="#FFF" stroke-width="2" fill="none" />
  <rect x="130" y="450" width="20" height="10" rx="2" ry="2" stroke="#FFF" stroke-width="2" fill="none" />
  <polygon points="175,450 185,455 175,460" stroke="#FFF" stroke-width="2" fill="none" />
</svg> 
<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="300" height="600" viewBox="0 0 300 600">
  <!-- iPhone Background -->
  <defs>
    <linearGradient id="iphone-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E3E3E3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#C0C0C0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="screen-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#4D90FE;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1976D2;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="5" />
      <feOffset dx="0" dy="0" />
      <feComposite in2="SourceGraphic" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0" />
      <feBlend in="SourceGraphic" in2="blurOut" mode="normal" />
    </filter>
    <linearGradient id="button-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E0E0E0;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Phone Body -->
  <rect x="60" y="20" width="180" height="560" rx="40" ry="40" fill="url(#iphone-gradient)" filter="url(#shadow)" />
  
  <!-- Screen -->
  <rect x="75" y="60" width="150" height="480" rx="5" ry="5" fill="url(#screen-gradient)" />
  
  <!-- Home Button -->
  <circle cx="150" cy="555" r="15" fill="url(#button-gradient)" stroke="#C0C0C0" stroke-width="1" />
  
  <!-- Camera -->
  <circle cx="150" cy="40" r="5" fill="#333" />
  
  <!-- Volume Buttons -->
  <rect x="50" y="120" width="5" height="30" rx="2" ry="2" fill="#B0B0B0" />
  <rect x="50" y="160" width="5" height="30" rx="2" ry="2" fill="#B0B0B0" />
  
  <!-- Power Button -->
  <rect x="245" y="150" width="5" height="40" rx="2" ry="2" fill="#B0B0B0" />
  
  <!-- App Icons - 3x4 grid -->
  <g transform="translate(85, 80)">
    <!-- Row 1 -->
    <rect x="0" y="0" width="30" height="30" rx="6" ry="6" fill="#FF5722" />
    <rect x="45" y="0" width="30" height="30" rx="6" ry="6" fill="#4CAF50" />
    <rect x="90" y="0" width="30" height="30" rx="6" ry="6" fill="#2196F3" />
    
    <!-- Row 2 -->
    <rect x="0" y="45" width="30" height="30" rx="6" ry="6" fill="#FFC107" />
    <rect x="45" y="45" width="30" height="30" rx="6" ry="6" fill="#9C27B0" />
    <rect x="90" y="45" width="30" height="30" rx="6" ry="6" fill="#E91E63" />
    
    <!-- Row 3 -->
    <rect x="0" y="90" width="30" height="30" rx="6" ry="6" fill="#673AB7" />
    <rect x="45" y="90" width="30" height="30" rx="6" ry="6" fill="#009688" />
    <rect x="90" y="90" width="30" height="30" rx="6" ry="6" fill="#795548" />
    
    <!-- Row 4 -->
    <rect x="0" y="135" width="30" height="30" rx="6" ry="6" fill="#3F51B5" />
    <rect x="45" y="135" width="30" height="30" rx="6" ry="6" fill="#FFEB3B" />
    <rect x="90" y="135" width="30" height="30" rx="6" ry="6" fill="#8BC34A" />
  </g>
  
  <!-- Photo on the screen -->
  <rect x="85" y="280" width="130" height="200" rx="5" ry="5" fill="#000" />
  <circle cx="150" cy="380" r="60" fill="#444" />
  <circle cx="150" cy="380" r="55" fill="#333" />
  <circle cx="150" cy="380" r="50" fill="#F5F5F5" />
  <circle cx="150" cy="380" r="25" fill="#FFC107" />
  
  <!-- Camera UI -->
  <circle cx="150" cy="510" r="20" stroke="#FFF" stroke-width="2" fill="none" />
  <rect x="110" y="510" width="15" height="15" rx="2" ry="2" fill="#FFF" />
</svg> 
/* Shopping Cart Page Styles */

.cart-section {
    padding: 60px 0;
}

.cart-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-bottom: 60px;
}

/* Cart Items */
.cart-items, .cart-summary {
    background-color: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.cart-items h2, .cart-summary h2 {
    font-size: 24px;
    margin-bottom: 20px;
    color: var(--dark-color);
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
}

/* Empty Cart Message */
.empty-cart {
    text-align: center;
    padding: 40px 20px;
}

.empty-cart i {
    font-size: 64px;
    color: #ddd;
    margin-bottom: 20px;
}

.empty-cart h3 {
    font-size: 24px;
    margin-bottom: 15px;
    color: var(--dark-color);
}

.empty-cart p {
    margin-bottom: 25px;
    color: #777;
}

/* Cart Table */
.cart-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.cart-table th, .cart-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.cart-table th {
    font-weight: 600;
    color: var(--dark-color);
}

.cart-table th:last-child, .cart-table td:last-child {
    text-align: center;
}

.product-info-cart {
    display: flex;
    align-items: center;
}

.product-image-cart {
    width: 80px;
    height: 80px;
    margin-right: 15px;
    border-radius: 4px;
    overflow: hidden;
}

.product-image-cart img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-name-cart {
    font-weight: 500;
}

.product-price-cart {
    color: var(--primary-color);
    font-weight: 500;
}

.quantity-selector-cart {
    display: flex;
    align-items: center;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    width: 120px;
}

.quantity-btn-cart {
    width: 30px;
    height: 32px;
    background-color: #f5f5f5;
    border: none;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s;
}

.quantity-btn-cart:hover {
    background-color: #e5e5e5;
}

.cart-quantity-input {
    width: 60px;
    height: 32px;
    border: none;
    text-align: center;
    font-size: 14px;
}

.product-total-cart {
    font-weight: 600;
    color: var(--dark-color);
}

.remove-item-btn {
    background-color: transparent;
    border: none;
    color: #999;
    font-size: 18px;
    cursor: pointer;
    transition: color 0.3s;
}

.remove-item-btn:hover {
    color: var(--error-color);
}

/* Cart Actions */
.cart-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
}

.coupon-form {
    display: flex;
    gap: 10px;
}

#coupon-code {
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    width: 200px;
}

/* Order Summary */
.summary-items {
    margin-bottom: 25px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px dashed #eee;
}

.summary-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
    margin-bottom: 0;
}

.summary-item.total {
    font-size: 20px;
    font-weight: 600;
    color: var(--dark-color);
    border-top: 1px solid #ddd;
    padding-top: 15px;
    margin-top: 15px;
}

.discount-row {
    color: var(--success-color);
}

.checkout-btn {
    width: 100%;
    padding: 12px;
    margin-bottom: 20px;
}

.checkout-btn:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

/* Payment Methods */
.payment-methods {
    text-align: center;
}

.payment-methods p {
    margin-bottom: 10px;
    font-size: 14px;
    color: #777;
}

.payment-icons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.payment-icons i {
    font-size: 30px;
    color: #666;
}

/* Recently Viewed Section */
.recently-viewed {
    margin-top: 60px;
}

.recently-viewed .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
}

/* Responsive Design */
@media (max-width: 992px) {
    .cart-container {
        grid-template-columns: 1fr;
    }
    
    .cart-actions {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .coupon-form {
        width: 100%;
    }
    
    #coupon-code {
        flex-grow: 1;
    }
    
    #clear-cart {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .cart-table {
        display: block;
        overflow-x: auto;
    }
    
    .cart-table th, .cart-table td {
        white-space: nowrap;
    }
    
    .coupon-form {
        flex-direction: column;
    }
    
    #coupon-code {
        width: 100%;
    }
    
    #apply-coupon {
        width: 100%;
    }
} 
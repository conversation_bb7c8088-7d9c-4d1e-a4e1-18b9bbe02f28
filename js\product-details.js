// Product Details Page JavaScript
document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const productLoading = document.getElementById('product-loading');
    const productDetails = document.getElementById('product-details');
    const productName = document.getElementById('product-name');
    const productBreadcrumb = document.getElementById('product-breadcrumb');
    const mainProductImage = document.getElementById('main-product-image');
    const productThumbnails = document.getElementById('product-thumbnails');
    const productBrand = document.getElementById('product-brand');
    const productPrice = document.getElementById('product-price');
    const productOriginalPrice = document.getElementById('product-original-price');
    const productDiscount = document.getElementById('product-discount');
    const productDescription = document.getElementById('product-description');
    const productStockStatus = document.getElementById('product-stock-status');
    const productStockCount = document.getElementById('product-stock-count');
    const productQuantity = document.getElementById('product-quantity');
    const decreaseQuantityBtn = document.getElementById('decrease-quantity');
    const increaseQuantityBtn = document.getElementById('increase-quantity');
    const addToCartBtn = document.getElementById('add-to-cart');
    const addToWishlistBtn = document.getElementById('add-to-wishlist');
    const specificationsTable = document.getElementById('specifications-table');
    const reviewsList = document.getElementById('reviews-list');
    const averageRating = document.getElementById('average-rating');
    const totalReviews = document.getElementById('total-reviews');
    const relatedProducts = document.getElementById('related-products');
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    const reviewForm = document.getElementById('review-form');
    const ratingSelector = document.querySelector('.rating-selector');
    const reviewRating = document.getElementById('review-rating');

    // State
    let product = null;
    let quantity = 1;
    let selectedRating = 0;

    // Initialize
    init();

    // Initialize the page
    async function init() {
        // Add event listeners
        decreaseQuantityBtn.addEventListener('click', decreaseQuantity);
        increaseQuantityBtn.addEventListener('click', increaseQuantity);
        productQuantity.addEventListener('change', updateQuantity);
        addToCartBtn.addEventListener('click', addToCart);
        addToWishlistBtn.addEventListener('click', toggleWishlist);
        
        // Tab functionality
        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const tab = btn.dataset.tab;
                switchTab(tab);
            });
        });
        
        // Rating selector for reviews
        if (ratingSelector) {
            const stars = ratingSelector.querySelectorAll('i');
            stars.forEach(star => {
                star.addEventListener('click', () => {
                    const rating = parseInt(star.dataset.rating);
                    updateRatingSelector(rating);
                });
                star.addEventListener('mouseover', () => {
                    const rating = parseInt(star.dataset.rating);
                    highlightStars(rating);
                });
                star.addEventListener('mouseout', () => {
                    highlightStars(selectedRating);
                });
            });
        }
        
        // Review form submission
        if (reviewForm) {
            reviewForm.addEventListener('submit', submitReview);
        }
        
        // Get product ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        const productId = urlParams.get('id');
        
        if (productId) {
            // Fetch product details
            await fetchProductDetails(productId);
            
            // Fetch related products
            await fetchRelatedProducts(productId);
        } else {
            // No product ID found, redirect to products page
            window.location.href = 'products.html';
        }
    }

    // Fetch product details from API
    async function fetchProductDetails(productId) {
        try {
            const response = await fetch(`${API_URL}/products/${productId}`);
            const data = await response.json();
            
            if (data) {
                product = data;
                renderProductDetails();
                fetchProductReviews(productId);
                return;
            }
            
            // Fallback to sample data if API fails or returns unexpected format
            generateSampleProduct(productId);
            
        } catch (error) {
            console.error('Error fetching product details:', error);
            // Fallback to sample data
            generateSampleProduct(productId);
        }
    }

    // Generate sample product for fallback
    function generateSampleProduct(productId) {
        const sampleBrands = [
            { id: 1, name: 'Apple' },
            { id: 2, name: 'Samsung' },
            { id: 3, name: 'Google' },
            { id: 4, name: 'OnePlus' },
            { id: 5, name: 'Xiaomi' }
        ];
        
        // Choose a brand based on product ID
        const brandIndex = (parseInt(productId) - 1) % sampleBrands.length;
        const brand = sampleBrands[brandIndex];
        
        // Generate specifications based on product ID
        const specifications = {
            display: ['6.1-inch OLED', '6.7-inch AMOLED', '6.4-inch AMOLED', '6.8-inch OLED'][productId % 4],
            processor: ['A15 Bionic', 'Snapdragon 888', 'Google Tensor', 'Exynos 2100'][productId % 4],
            ram: ['6GB', '8GB', '12GB', '16GB'][productId % 4],
            storage: ['128GB', '256GB', '512GB', '1TB'][productId % 4],
            battery: ['4000mAh', '4500mAh', '5000mAh', '4800mAh'][productId % 4],
            camera: ['12MP triple', '108MP quad', '50MP + 12MP', '64MP triple'][productId % 4],
            os: ['iOS 15', 'Android 12', 'Android 13', 'Android 11'][productId % 4]
        };
        
        // Generate a discount (20% chance of discount)
        const hasDiscount = Math.random() < 0.2;
        const discountPercentage = hasDiscount ? Math.floor(Math.random() * 30) + 10 : 0; // 10-40% discount
        const basePrice = 500 + (productId * 100); // Base price increases with ID
        const discountedPrice = hasDiscount ? basePrice * (1 - discountPercentage / 100) : basePrice;
        
        // Map product IDs to specific product images
        const productImages = {
            1: 'images/iphone.svg',
            2: 'images/samsung.svg',
            3: 'images/pixel.svg',
            4: 'images/phone1.svg',
            5: 'images/phone2.svg',
            6: 'images/ipad.svg',
            7: 'images/galaxy-tab.svg',
            8: 'images/case.svg',
            9: 'images/charger.svg',
            10: 'images/screen-protector.svg'
        };
        
        // Choose image based on product ID or category
        let imageUrl = productImages[productId] || 'images/placeholder.svg';
        
        // For IDs beyond our specific mappings
        if (productId > 10) {
            if (productId % 3 === 0) {
                // Accessories
                const accessoryImages = ['images/case.svg', 'images/charger.svg', 'images/screen-protector.svg'];
                imageUrl = accessoryImages[productId % accessoryImages.length];
            } else if (productId % 3 === 1) {
                // Smartphones
                imageUrl = [`images/phone1.svg`, `images/phone2.svg`, `images/iphone.svg`, `images/samsung.svg`, `images/pixel.svg`][productId % 5];
            } else {
                // Tablets
                imageUrl = productId % 2 === 0 ? 'images/ipad.svg' : 'images/galaxy-tab.svg';
            }
        }
        
        product = {
            product_id: parseInt(productId),
            name: `Product ${productId} - Premium Smartphone`,
            description: `This is a sample product description for Product ${productId}. It's a high-quality smartphone with excellent features including a ${specifications.display} display, ${specifications.processor} processor, ${specifications.ram} RAM, and ${specifications.storage} storage. The ${specifications.camera} camera system delivers stunning photos in any lighting conditions.`,
            price: discountedPrice.toFixed(2),
            original_price: hasDiscount ? basePrice.toFixed(2) : null,
            discount_percentage: hasDiscount ? discountPercentage : 0,
            brand_id: brand.id,
            brand_name: brand.name,
            category_id: 1,
            category_name: 'Smartphones',
            stock_quantity: Math.floor(Math.random() * 50),
            specifications: specifications,
            images: [
                { image_url: imageUrl },
                { image_url: 'images/placeholder.svg' },
                { image_url: 'images/placeholder.svg' }
            ],
            reviews: [
                {
                    review_id: 1,
                    user_name: 'John Doe',
                    rating: 5,
                    comment: 'Excellent product, very satisfied with the purchase. The camera quality is amazing and battery life exceeds expectations.',
                    created_at: '2023-03-15'
                },
                {
                    review_id: 2,
                    user_name: 'Jane Smith',
                    rating: 4,
                    comment: 'Great phone overall. Fast performance and good battery life. Could improve the camera quality in low light.',
                    created_at: '2023-02-20'
                }
            ],
            average_rating: 4.5,
            review_count: 2
        };
        
        renderProductDetails();
        renderProductReviews();
    }

    // Fetch product reviews from API
    async function fetchProductReviews(productId) {
        try {
            const response = await fetch(`${API_URL}/products/${productId}/reviews`);
            const data = await response.json();
            
            if (Array.isArray(data)) {
                product.reviews = data;
                product.review_count = data.length;
                
                // Calculate average rating
                if (data.length > 0) {
                    const totalRating = data.reduce((sum, review) => sum + review.rating, 0);
                    product.average_rating = totalRating / data.length;
                } else {
                    product.average_rating = 0;
                }
                
                renderProductReviews();
                return;
            }
            
            // If already have sample reviews from generateSampleProduct, use those
            if (product.reviews) {
                renderProductReviews();
            }
            
        } catch (error) {
            console.error('Error fetching product reviews:', error);
            // If already have sample reviews from generateSampleProduct, use those
            if (product.reviews) {
                renderProductReviews();
            }
        }
    }

    // Fetch related products from API
    async function fetchRelatedProducts(productId) {
        try {
            const response = await fetch(`${API_URL}/products?category=${product.category_id}&limit=4&exclude=${productId}`);
            const data = await response.json();
            
            if (Array.isArray(data) && data.length > 0) {
                renderRelatedProducts(data);
                return;
            }
            
            // Fallback to sample related products
            generateSampleRelatedProducts();
            
        } catch (error) {
            console.error('Error fetching related products:', error);
            // Fallback to sample related products
            generateSampleRelatedProducts();
        }
    }

    // Generate sample related products for fallback
    function generateSampleRelatedProducts() {
        const relatedProductsData = [];
        
        const productImages = {
            1: 'images/iphone.svg',
            2: 'images/samsung.svg',
            3: 'images/pixel.svg',
            4: 'images/phone1.svg',
            5: 'images/phone2.svg',
            6: 'images/ipad.svg',
            7: 'images/galaxy-tab.svg',
            8: 'images/case.svg',
            9: 'images/charger.svg',
            10: 'images/screen-protector.svg'
        };
        
        for (let i = 1; i <= 4; i++) {
            const id = (product.product_id + i) % 10 + 1; // Ensure ID is different from current product
            if (id === product.product_id) continue;
            
            relatedProductsData.push({
                product_id: id,
                name: `Related Product ${id}`,
                price: (299.99 + (id * 100)).toFixed(2),
                images: [{ image_url: productImages[id] || 'images/placeholder.svg' }]
            });
        }
        
        renderRelatedProducts(relatedProductsData);
    }

    // Render product details to the page
    function renderProductDetails() {
        // Update page title and breadcrumb
        document.title = `${product.name} - PhoneHub`;
        productBreadcrumb.textContent = product.name;
        
        // Update product info
        productName.textContent = product.name;
        productBrand.textContent = product.brand_name || 'Unknown Brand';
        productPrice.textContent = `$${parseFloat(product.price).toFixed(2)}`;
        
        // Show/hide original price and discount badge if there's a discount
        if (product.original_price) {
            productOriginalPrice.textContent = `$${parseFloat(product.original_price).toFixed(2)}`;
            productOriginalPrice.style.display = 'inline';
            
            productDiscount.textContent = `${product.discount_percentage}% OFF`;
            productDiscount.style.display = 'inline';
        } else {
            productOriginalPrice.style.display = 'none';
            productDiscount.style.display = 'none';
        }
        
        // Update description
        productDescription.textContent = product.description;
        
        // Update stock status
        if (product.stock_quantity > 0) {
            productStockStatus.textContent = 'In Stock';
            productStockStatus.className = 'in-stock';
            productStockCount.textContent = `(${product.stock_quantity} available)`;
            addToCartBtn.disabled = false;
        } else {
            productStockStatus.textContent = 'Out of Stock';
            productStockStatus.className = 'out-of-stock';
            productStockCount.textContent = '';
            addToCartBtn.disabled = true;
        }
        
        // Update main image
        if (product.images && product.images.length > 0) {
            mainProductImage.src = product.images[0].image_url;
            mainProductImage.alt = product.name;
            
            // Add thumbnails
            productThumbnails.innerHTML = '';
            
            product.images.forEach((image, index) => {
                const thumbnail = document.createElement('div');
                thumbnail.className = 'thumbnail-item' + (index === 0 ? ' active' : '');
                thumbnail.innerHTML = `<img src="${image.image_url}" alt="${product.name} - Image ${index + 1}">`;
                
                thumbnail.addEventListener('click', () => {
                    // Update main image when thumbnail is clicked
                    mainProductImage.src = image.image_url;
                    
                    // Update active state
                    document.querySelectorAll('.thumbnail-item').forEach(item => {
                        item.classList.remove('active');
                    });
                    thumbnail.classList.add('active');
                });
                
                productThumbnails.appendChild(thumbnail);
            });
        }
        
        // Render specifications
        renderSpecifications();
        
        // Show product details and hide loading skeleton
        productLoading.style.display = 'none';
        productDetails.style.display = 'grid';
    }

    // Render specifications table
    function renderSpecifications() {
        specificationsTable.innerHTML = '';
        
        if (product.specifications) {
            // For object format
            if (typeof product.specifications === 'object' && !Array.isArray(product.specifications)) {
                Object.entries(product.specifications).forEach(([key, value]) => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${formatSpecName(key)}</td>
                        <td>${value}</td>
                    `;
                    specificationsTable.appendChild(row);
                });
            }
            // For array format
            else if (Array.isArray(product.specifications)) {
                product.specifications.forEach(spec => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${spec.name || formatSpecName(spec.key)}</td>
                        <td>${spec.value}</td>
                    `;
                    specificationsTable.appendChild(row);
                });
            }
        } else {
            // No specifications available
            specificationsTable.innerHTML = '<tr><td colspan="2">No specifications available for this product.</td></tr>';
        }
    }

    // Format specification name (convert snake_case or camelCase to Title Case)
    function formatSpecName(name) {
        return name
            .replace(/_/g, ' ')
            .replace(/([A-Z])/g, ' $1')
            .replace(/^./, function(str) { return str.toUpperCase(); });
    }

    // Render product reviews
    function renderProductReviews() {
        // Update average rating and total reviews
        averageRating.textContent = product.average_rating ? product.average_rating.toFixed(1) : '0.0';
        totalReviews.textContent = product.review_count || 0;
        
        // Update review list
        reviewsList.innerHTML = '';
        
        if (product.reviews && product.reviews.length > 0) {
            product.reviews.forEach(review => {
                const reviewItem = document.createElement('div');
                reviewItem.className = 'review-item';
                
                // Format date
                const reviewDate = new Date(review.created_at);
                const formattedDate = reviewDate.toLocaleDateString('en-US', { 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                });
                
                // Generate stars
                let stars = '';
                for (let i = 1; i <= 5; i++) {
                    if (i <= review.rating) {
                        stars += '<i class="fas fa-star"></i>';
                    } else if (i - review.rating < 1) {
                        stars += '<i class="fas fa-star-half-alt"></i>';
                    } else {
                        stars += '<i class="far fa-star"></i>';
                    }
                }
                
                reviewItem.innerHTML = `
                    <div class="review-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="review-content">
                        <div class="review-header">
                            <span class="review-author">${review.user_name}</span>
                            <span class="review-date">${formattedDate}</span>
                        </div>
                        <div class="review-rating">
                            ${stars}
                        </div>
                        <p class="review-text">${review.comment}</p>
                    </div>
                `;
                
                reviewsList.appendChild(reviewItem);
            });
            
            // Update rating distribution
            updateRatingDistribution();
        } else {
            reviewsList.innerHTML = '<div class="no-reviews">No reviews yet. Be the first to review this product!</div>';
        }
    }

    // Update rating distribution bars
    function updateRatingDistribution() {
        if (!product.reviews || product.reviews.length === 0) return;
        
        // Count ratings for each star level
        const ratingCounts = [0, 0, 0, 0, 0]; // 5, 4, 3, 2, 1 stars
        
        product.reviews.forEach(review => {
            if (review.rating >= 1 && review.rating <= 5) {
                ratingCounts[5 - review.rating]++;
            }
        });
        
        // Update rating bars and counts
        const ratingBars = document.querySelectorAll('.rating-bar');
        
        ratingBars.forEach((bar, index) => {
            const count = ratingCounts[index];
            const percentage = (count / product.reviews.length) * 100;
            
            bar.querySelector('.bar').style.width = `${percentage}%`;
            bar.querySelector('.rating-count').textContent = count;
        });
    }

    // Render related products
    function renderRelatedProducts(relatedProductsData) {
        relatedProducts.innerHTML = '';
        
        relatedProductsData.forEach(product => {
            const productCard = createProductCard(product);
            relatedProducts.appendChild(productCard);
        });
        
        // Note: Add to cart event listeners are handled globally in app.js
    }

    // Decrease quantity
    function decreaseQuantity() {
        if (quantity > 1) {
            quantity--;
            productQuantity.value = quantity;
        }
    }

    // Increase quantity
    function increaseQuantity() {
        if (quantity < product.stock_quantity) {
            quantity++;
            productQuantity.value = quantity;
        }
    }

    // Update quantity when input changes
    function updateQuantity() {
        const newQuantity = parseInt(productQuantity.value);
        
        if (isNaN(newQuantity) || newQuantity < 1) {
            quantity = 1;
        } else if (newQuantity > product.stock_quantity) {
            quantity = product.stock_quantity;
        } else {
            quantity = newQuantity;
        }
        
        productQuantity.value = quantity;
    }

    // Add to cart
    function addToCart() {
        if (product.stock_quantity < 1) return;
        
        // Get current cart from localStorage
        let cart = [];
        const savedCart = localStorage.getItem('cart');
        if (savedCart) {
            try {
                cart = JSON.parse(savedCart);
            } catch (e) {
                console.error('Error parsing cart from localStorage:', e);
                cart = [];
            }
        }
        
        // Check if product is already in cart
        const existingItemIndex = cart.findIndex(item => item.id === product.product_id.toString());
        
        if (existingItemIndex >= 0) {
            // Update quantity if already in cart
            cart[existingItemIndex].quantity += quantity;
        } else {
            // Add new item to cart
            cart.push({
                id: product.product_id.toString(),
                name: product.name,
                price: parseFloat(product.price),
                image: product.images && product.images.length > 0 ? product.images[0].image_url : 'images/placeholder.svg',
                quantity: quantity
            });
        }
        
        // Save cart to localStorage
        localStorage.setItem('cart', JSON.stringify(cart));
        
        // Update cart count in header
        updateCartCount();
        
        // Show success message
        showNotification('Product added to cart!');
    }

    // Toggle wishlist
    function toggleWishlist() {
        const heartIcon = addToWishlistBtn.querySelector('i');
        
        if (heartIcon.classList.contains('far')) {
            // Add to wishlist
            heartIcon.classList.remove('far');
            heartIcon.classList.add('fas');
            heartIcon.style.color = 'var(--accent-color)';
            
            showNotification('Product added to wishlist!');
        } else {
            // Remove from wishlist
            heartIcon.classList.remove('fas');
            heartIcon.classList.add('far');
            heartIcon.style.color = '';
            
            showNotification('Product removed from wishlist!');
        }
    }

    // Switch tabs
    function switchTab(tabName) {
        // Update active state for tab buttons
        tabBtns.forEach(btn => {
            if (btn.dataset.tab === tabName) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });
        
        // Show active tab content
        tabContents.forEach(content => {
            if (content.id === tabName + '-tab') {
                content.classList.add('active');
            } else {
                content.classList.remove('active');
            }
        });
    }

    // Update rating selector
    function updateRatingSelector(rating) {
        selectedRating = rating;
        reviewRating.value = rating;
        highlightStars(rating);
    }

    // Highlight stars in rating selector
    function highlightStars(rating) {
        const stars = ratingSelector.querySelectorAll('i');
        
        stars.forEach((star, index) => {
            if (index < rating) {
                star.className = 'fas fa-star active';
            } else {
                star.className = 'far fa-star';
            }
        });
    }

    // Submit review
    function submitReview(e) {
        e.preventDefault();
        
        // Validate rating
        if (selectedRating === 0) {
            alert('Please select a rating');
            return;
        }
        
        // Get form data
        const name = document.getElementById('review-name').value;
        const email = document.getElementById('review-email').value;
        const comment = document.getElementById('review-comment').value;
        
        // In a real application, this would send the review to the server
        // For now, let's just add it to the local reviews array
        
        // Create new review object
        const newReview = {
            review_id: product.reviews ? product.reviews.length + 1 : 1,
            user_name: name,
            rating: selectedRating,
            comment: comment,
            created_at: new Date().toISOString()
        };
        
        // Add to reviews array
        if (!product.reviews) {
            product.reviews = [];
        }
        
        product.reviews.unshift(newReview);
        
        // Update review count and average rating
        product.review_count = product.reviews.length;
        
        const totalRating = product.reviews.reduce((sum, review) => sum + review.rating, 0);
        product.average_rating = totalRating / product.reviews.length;
        
        // Render updated reviews
        renderProductReviews();
        
        // Reset form
        reviewForm.reset();
        updateRatingSelector(0);
        
        // Show success message
        showNotification('Your review has been submitted!');
        
        // Switch to reviews tab
        switchTab('reviews');
    }
}); 
<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="300" height="400" viewBox="0 0 300 400">
  <!-- Phone Case Background -->
  <defs>
    <linearGradient id="case-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F57C00;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="clear-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#EEEEEE;stop-opacity:0.4" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="5" />
      <feOffset dx="0" dy="0" />
      <feComposite in2="SourceGraphic" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0" />
      <feBlend in="SourceGraphic" in2="blurOut" mode="normal" />
    </filter>
    <pattern id="pattern-marble" width="100" height="100" patternUnits="userSpaceOnUse">
      <rect width="100" height="100" fill="#FFFFFF" />
      <path d="M0,0 L100,100 M100,0 L0,100" stroke="#EEEEEE" stroke-width="5" />
      <path d="M25,0 L125,100 M0,25 L100,125" stroke="#F5F5F5" stroke-width="3" />
      <path d="M75,0 L175,100 M0,75 L100,175" stroke="#FAFAFA" stroke-width="4" />
    </pattern>
  </defs>

  <!-- Case Body -->
  <rect x="70" y="30" width="160" height="340" rx="20" ry="20" fill="url(#case-gradient)" filter="url(#shadow)" />
  
  <!-- Inner Case -->
  <rect x="80" y="40" width="140" height="320" rx="12" ry="12" fill="#FFFFFF" opacity="0.1" />
  
  <!-- Pattern Overlay - can be adjusted for different case styles -->
  <rect x="70" y="30" width="160" height="340" rx="20" ry="20" fill="url(#pattern-marble)" opacity="0.3" />
  
  <!-- Case Edge Details (bumper) -->
  <rect x="70" y="30" width="160" height="8" rx="4" ry="4" fill="#FFB74D" />
  <rect x="70" y="362" width="160" height="8" rx="4" ry="4" fill="#FFB74D" />
  <rect x="70" y="30" width="8" height="340" rx="4" ry="4" fill="#FFB74D" />
  <rect x="222" y="30" width="8" height="340" rx="4" ry="4" fill="#FFB74D" />
  
  <!-- Camera Cutout -->
  <rect x="200" y="60" width="20" height="20" rx="5" ry="5" fill="#333333" />
  
  <!-- Button Cutouts -->
  <rect x="65" y="100" width="10" height="40" rx="5" ry="5" fill="#333333" />
  <rect x="225" y="120" width="10" height="30" rx="5" ry="5" fill="#333333" />
  
  <!-- Case Details (textures) -->
  <line x1="90" y1="150" x2="210" y2="150" stroke="#FFFFFF" stroke-width="1" opacity="0.3" />
  <line x1="90" y1="160" x2="210" y2="160" stroke="#FFFFFF" stroke-width="1" opacity="0.3" />
  <line x1="90" y1="170" x2="210" y2="170" stroke="#FFFFFF" stroke-width="1" opacity="0.3" />
  <line x1="90" y1="180" x2="210" y2="180" stroke="#FFFFFF" stroke-width="1" opacity="0.3" />
  <line x1="90" y1="190" x2="210" y2="190" stroke="#FFFFFF" stroke-width="1" opacity="0.3" />
  
  <!-- Embossed Brand Logo -->
  <circle cx="150" cy="250" r="30" fill="#FFFFFF" opacity="0.1" />
  <text x="150" y="258" font-family="Arial" font-size="16" font-weight="bold" text-anchor="middle" fill="#FFFFFF" opacity="0.6">CASE</text>
  
  <!-- Bottom Port Cutouts -->
  <rect x="120" y="365" width="60" height="10" rx="5" ry="5" fill="#333333" />
  
  <!-- Highlight Reflection -->
  <path d="M80,50 L90,60 L90,150 L80,140 Z" fill="#FFFFFF" opacity="0.2" />
</svg> 
<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="400" height="400" viewBox="0 0 400 400">
  <!-- Charger Background -->
  <defs>
    <linearGradient id="adapter-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F5F5F5;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cable-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#212121;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#424242;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="3" />
      <feOffset dx="2" dy="2" />
      <feComposite in2="SourceGraphic" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
      <feBlend in="SourceGraphic" in2="blurOut" mode="normal" />
    </filter>
    <filter id="inner-shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="1" />
      <feOffset dx="0" dy="1" />
      <feComposite in2="SourceGraphic" operator="arithmetic" k1="0" k2="1" k3="1" k4="0" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0" />
    </filter>
  </defs>

  <!-- Power Adapter -->
  <rect x="150" y="50" width="100" height="100" rx="10" ry="10" fill="url(#adapter-gradient)" filter="url(#shadow)" />
  
  <!-- Power Adapter Details -->
  <rect x="165" y="60" width="70" height="80" rx="5" ry="5" fill="#FAFAFA" filter="url(#inner-shadow)" />
  <text x="200" y="105" font-family="Arial" font-size="10" font-weight="bold" text-anchor="middle" fill="#757575">20W</text>
  <text x="200" y="120" font-family="Arial" font-size="8" text-anchor="middle" fill="#757575">USB-C PD</text>
  
  <!-- USB-C Port -->
  <rect x="195" y="150" width="10" height="5" rx="1" ry="1" fill="#212121" />
  <rect x="192" y="148" width="16" height="9" rx="2" ry="2" fill="none" stroke="#E0E0E0" stroke-width="1" />
  
  <!-- Prongs -->
  <rect x="175" y="35" width="10" height="15" rx="2" ry="2" fill="#BDBDBD" />
  <rect x="215" y="35" width="10" height="15" rx="2" ry="2" fill="#BDBDBD" />
  
  <!-- Cable -->
  <path d="M200,155 C200,155 200,200 230,230 C260,260 200,300 200,350" stroke="url(#cable-gradient)" stroke-width="10" fill="none" stroke-linecap="round" />
  
  <!-- Cable Details -->
  <path d="M200,155 C200,155 200,200 230,230 C260,260 200,300 200,350" stroke="#000000" stroke-width="10" fill="none" stroke-linecap="round" stroke-opacity="0.1" stroke-dasharray="1,10" />
  
  <!-- USB-C Connector -->
  <rect x="190" y="350" width="20" height="30" rx="3" ry="3" fill="#212121" filter="url(#shadow)" />
  <rect x="195" y="350" width="10" height="2" rx="1" ry="1" fill="#424242" />
  <rect x="195" y="354" width="10" height="2" rx="1" ry="1" fill="#616161" />
  <rect x="195" y="358" width="10" height="2" rx="1" ry="1" fill="#424242" />
  <rect x="188" y="363" width="24" height="20" rx="4" ry="4" fill="#212121" filter="url(#shadow)" />
  
  <!-- USB-C Connector Details -->
  <rect x="198" y="374" width="4" height="6" rx="1" ry="1" fill="#757575" />
  <path d="M187,368 L213,368" stroke="#424242" stroke-width="1" />
  
  <!-- Branding -->
  <circle cx="200" y="83" r="15" fill="#F5F5F5" />
  <text x="200" y="87" font-family="Arial" font-size="10" font-weight="bold" text-anchor="middle" fill="#616161">PH</text>
</svg> 
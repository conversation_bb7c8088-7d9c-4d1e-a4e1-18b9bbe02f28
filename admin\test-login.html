<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PhoneHub Admin - Test Login</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }
        .login-box {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            width: 350px;
        }
        h1 {
            text-align: center;
            color: #5a55b9;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #5a55b9;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #7f78d2;
        }
        .error-message {
            color: red;
            margin-top: 15px;
            text-align: center;
            display: none;
        }
        .success-message {
            color: green;
            margin-top: 15px;
            text-align: center;
            display: none;
        }
        .debug-log {
            margin-top: 20px;
            padding: 10px;
            background-color: #333;
            color: white;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-box">
        <h1>Test Admin Login</h1>
        <form id="test-login-form">
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" value="Admin@123" required>
            </div>
            <button type="submit">Login</button>
        </form>
        <div id="error-message" class="error-message"></div>
        <div id="success-message" class="success-message"></div>
        <div id="debug-log" class="debug-log"></div>
    </div>

    <script>
        // Debug helper
        function log(message) {
            const debugLog = document.getElementById('debug-log');
            debugLog.style.display = 'block';
            debugLog.innerHTML += message + '<br>';
            console.log(message);
        }

        // Show error
        function showError(message) {
            const errorMessage = document.getElementById('error-message');
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            log('Error: ' + message);
        }

        // Show success
        function showSuccess(message) {
            const successMessage = document.getElementById('success-message');
            successMessage.textContent = message;
            successMessage.style.display = 'block';
            log('Success: ' + message);
        }

        // Handle login form submission
        document.getElementById('test-login-form').addEventListener('submit', function(e) {
            e.preventDefault();
            log('Login form submitted');

            // Get form values
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            log(`Email: ${email}`);
            log(`Password: ${password}`);

            // Check credentials
            if (email === '<EMAIL>' && password === 'Admin@123') {
                log('Credentials match!');
                
                // Store auth data in localStorage
                const token = 'admin-token-' + Date.now();
                const user = {
                    email: email,
                    name: 'Admin User',
                    role: 'admin'
                };
                
                localStorage.setItem('admin_token', token);
                localStorage.setItem('admin_user', JSON.stringify(user));
                
                log('Auth data stored in localStorage:');
                log(`admin_token: ${token}`);
                log(`admin_user: ${JSON.stringify(user)}`);
                
                showSuccess('Login successful! Auth data stored in localStorage.');
                
                // Add a link to the dashboard
                const successMessage = document.getElementById('success-message');
                successMessage.innerHTML += '<br><a href="index.html">Go to Dashboard</a>';
            } else {
                log('Credentials do not match!');
                log(`Expected: <EMAIL> / Admin@123`);
                log(`Got: ${email} / ${password}`);
                
                showError('Invalid email or password. Please try again.');
            }
        });

        // Check existing auth data on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('Page loaded');
            
            const adminToken = localStorage.getItem('admin_token');
            const adminUser = localStorage.getItem('admin_user');
            
            log('Current auth state:');
            log(`admin_token: ${adminToken || 'not set'}`);
            log(`admin_user: ${adminUser || 'not set'}`);
            
            if (adminToken && adminUser) {
                showSuccess('You are already logged in. Auth data found in localStorage.');
                // Add a link to the dashboard
                const successMessage = document.getElementById('success-message');
                successMessage.innerHTML += '<br><a href="index.html">Go to Dashboard</a>';
            }
        });
    </script>
</body>
</html> 
/* Admin Panel Styles */
:root {
    --primary-color: #5a55b9;
    --secondary-color: #7f78d2;
    --accent-color: #ff6b6b;
    --text-color: #333333;
    --light-color: #f9f9f9;
    --dark-color: #333333;
    --gray-color: #f0f0f0;
    --medium-gray: #9e9e9e;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --card-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    --hover-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    --border-radius: 10px;
    --transition-speed: 0.3s;
    --sidebar-width: 250px;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-color);
}

.admin-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--primary-color);
    color: white;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-header h1 {
    font-size: 1.5rem;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sidebar-header h1 i {
    margin-right: 10px;
}

.sidebar-header p {
    font-size: 0.9rem;
    opacity: 0.7;
}

.sidebar nav {
    flex: 1;
    padding: 20px 0;
}

.sidebar nav ul {
    list-style: none;
}

.sidebar nav ul li {
    margin-bottom: 5px;
}

.sidebar nav ul li a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all var(--transition-speed);
    border-radius: 4px;
    margin: 0 10px;
}

.sidebar nav ul li a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.sidebar nav ul li a:hover {
    color: white;
    background-color: rgba(255,255,255,0.1);
    transform: translateX(5px);
}

.sidebar nav ul li.active a {
    color: white;
    background-color: rgba(255,255,255,0.2);
    font-weight: 500;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.sidebar-footer a {
    display: block;
    padding: 10px;
    text-align: center;
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed);
}

.sidebar-footer a:hover {
    background-color: var(--accent-color);
}

/* Main Content */
.content {
    flex: 1;
    margin-left: var(--sidebar-width);
    padding: 20px;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.content-header h1 {
    font-size: 1.8rem;
    color: var(--primary-color);
    position: relative;
    padding-bottom: 10px;
}

.content-header h1::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background-color: var(--accent-color);
}

.admin-user {
    display: flex;
    align-items: center;
}

.admin-user span {
    margin-right: 10px;
    font-weight: 500;
}

.admin-user img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    box-shadow: var(--card-shadow);
}

/* Dashboard Stats */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--card-shadow);
    display: flex;
    align-items: center;
    transition: all var(--transition-speed);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.stat-icon {
    background-color: rgba(90, 85, 185, 0.1);
    color: var(--primary-color);
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    margin-right: 20px;
}

.stat-info h3 {
    font-size: 0.9rem;
    color: var(--medium-gray);
    margin-bottom: 5px;
}

.stat-info p {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--primary-color);
}

/* Charts */
.dashboard-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.chart-container {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--card-shadow);
    height: 350px;
    overflow: hidden;
    position: relative;
}

.chart-container h2 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    color: var(--dark-color);
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.chart-container canvas {
    width: 100% !important;
    height: calc(100% - 50px) !important;
}

/* Tables */
.recent-section {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--card-shadow);
}

.recent-section h2 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    color: var(--primary-color);
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th, 
table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

table th {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--medium-gray);
}

table tbody tr:hover {
    background-color: #f9f9f9;
}

.status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status.completed, .status-delivered {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

.status.pending, .status-pending {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
}

.status.cancelled, .status-cancelled {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
}

.status-processing {
    background-color: rgba(90, 85, 185, 0.1);
    color: var(--primary-color);
}

.status-shipped {
    background-color: rgba(0, 188, 212, 0.1);
    color: #00BCD4;
}

.view-all {
    display: block;
    margin-top: 20px;
    text-align: center;
    color: var(--primary-color);
    text-decoration: none;
}

.view-all:hover {
    text-decoration: underline;
}

/* Forms */
.form-container {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--card-shadow);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
}

.form-control, input[type="text"], input[type="email"], input[type="password"], input[type="number"], select, textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: all var(--transition-speed);
}

.form-control:focus, input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(90, 85, 185, 0.1);
}

/* Button styling */
.btn {
    display: inline-block;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-speed);
}

.primary-btn {
    background-color: var(--primary-color);
    color: white;
}

.primary-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
}

.secondary-btn {
    background-color: #eee;
    color: var(--text-color);
}

.secondary-btn:hover {
    background-color: #ddd;
    transform: translateY(-2px);
}

.danger-btn {
    background-color: var(--danger-color);
    color: white;
}

.danger-btn:hover {
    background-color: #d32f2f;
    transform: translateY(-2px);
}

.small-btn {
    padding: 6px 12px;
    font-size: 0.85rem;
    margin-right: 5px;
}

.edit-btn {
    background-color: var(--primary-color);
    color: white;
}

.edit-btn:hover {
    background-color: var(--secondary-color);
}

.delete-btn {
    background-color: var(--danger-color);
    color: white;
}

.delete-btn:hover {
    background-color: #d32f2f;
}

/* Admin Login Page Styles */
.login-page {
    background-color: var(--light-color);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
}

.admin-login-container {
    width: 100%;
    max-width: 400px;
    padding: 20px;
}

.login-box {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    padding: 30px;
    animation: fadeInDown 0.5s ease;
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-header h1 {
    font-size: 1.8rem;
    color: var(--primary-color);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-header h1 i {
    margin-right: 10px;
}

.login-header p {
    color: var(--medium-gray);
}

#admin-login-form .form-group {
    margin-bottom: 20px;
}

#admin-login-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
}

.input-with-icon {
    position: relative;
}

.input-with-icon i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--medium-gray);
}

.input-with-icon input {
    width: 100%;
    padding: 12px 12px 12px 40px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: all var(--transition-speed);
}

.input-with-icon input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(90, 85, 185, 0.1);
}

.checkbox-group {
    display: flex;
    align-items: center;
}

.checkbox-group input {
    margin-right: 8px;
}

#admin-login-form .primary-btn {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    cursor: pointer;
    transition: all var(--transition-speed);
}

#admin-login-form .primary-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: var(--card-shadow);
}

.form-footer {
    text-align: center;
    margin-top: 20px;
}

.form-footer a {
    color: var(--primary-color);
    text-decoration: none;
}

.form-footer a:hover {
    text-decoration: underline;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    align-items: center;
    justify-content: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background-color: white;
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--hover-shadow);
    margin: 30px auto;
    animation: fadeInDown 0.3s ease;
}

.modal-lg {
    max-width: 800px;
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    font-size: 1.5rem;
    margin: 0;
    color: var(--primary-color);
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--medium-gray);
    transition: color var(--transition-speed);
}

.close-btn:hover {
    color: var(--danger-color);
}

.modal-body {
    padding: 20px;
}

/* Action buttons in table */
.actions-cell {
    white-space: nowrap;
}

.action-btn {
    padding: 6px 10px;
    margin: 0 2px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all var(--transition-speed);
}

.action-btn.edit-btn {
    background-color: var(--primary-color);
    color: white;
}

.action-btn.edit-btn:hover {
    background-color: var(--secondary-color);
}

.action-btn.delete-btn {
    background-color: var(--danger-color);
    color: white;
}

.action-btn.delete-btn:hover {
    background-color: #d32f2f;
}

/* Notification Styles */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
}

.notification {
    padding: 15px 20px;
    margin-bottom: 10px;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    animation: slideInRight 0.3s ease-out;
    position: relative;
}

.notification.success {
    background-color: #d4edda;
    color: #155724;
    border-left: 4px solid #28a745;
}

.notification.error {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.notification.info {
    background-color: #d1ecf1;
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

.notification.fade-out {
    animation: fadeOut 0.3s ease-out forwards;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* Empty table message */
.empty-table {
    text-align: center;
    color: var(--medium-gray);
    font-style: italic;
    padding: 40px 20px;
}

/* Warning text in delete modal */
.warning-text {
    color: var(--danger-color);
    font-weight: 500;
    margin-top: 10px;
}

/* Form styles for modals */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.warning-text {
    color: var(--danger-color);
    font-size: 0.9rem;
    margin-top: 10px;
}

/* Data table container */
.data-table-container {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--card-shadow);
    margin-top: 20px;
}

.data-table {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    width: 100%;
}

.data-table table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.data-table th {
    background-color: #f9fafb;
    font-weight: 600;
    color: var(--text-color);
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table tr:hover {
    background-color: #f9fafb;
}

.data-table img {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 4px;
}

/* Brand logo in table */
.brand-logo {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

/* Search and action buttons */
.content-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
    background-color: white;
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
}

.search-bar {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
    max-width: 400px;
}

.search-bar input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
}

.search-bar button {
    padding: 10px 15px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-speed);
}

.search-bar button:hover {
    background-color: var(--secondary-color);
}

.filter-actions {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-actions select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    min-width: 120px;
}

/* Add Product Button */
#add-product-btn {
    white-space: nowrap;
    flex-shrink: 0;
    padding: 10px 20px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Ensure content actions don't overflow */
.content-actions {
    overflow: visible;
    min-height: 60px;
}

/* Fix table container width */
.data-table {
    width: 100%;
    max-width: 100%;
    overflow-x: auto;
}

.search-box {
    display: flex;
    align-items: center;
    background-color: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: all var(--transition-speed);
}

.search-box:focus-within {
    box-shadow: 0 0 0 3px rgba(90, 85, 185, 0.1);
}

.search-box input {
    padding: 10px 15px;
    border: none;
    width: 300px;
    font-size: 0.9rem;
}

.search-box input:focus {
    outline: none;
}

.search-box button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 15px;
    cursor: pointer;
    transition: background-color var(--transition-speed);
}

.search-box button:hover {
    background-color: var(--secondary-color);
}

.filter-group {
    margin-left: 10px;
}

.filter-group select {
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    background-color: white;
    font-size: 0.9rem;
    transition: all var(--transition-speed);
}

.filter-group select:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(90, 85, 185, 0.1);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
}

.pagination-btn {
    padding: 8px 15px;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    cursor: pointer;
    margin: 0 5px;
    font-size: 0.9rem;
    transition: all var(--transition-speed);
}

.pagination-btn:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#page-info {
    margin: 0 10px;
    font-size: 0.9rem;
}

/* Order details styling */
.order-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.order-details h3 {
    margin-bottom: 15px;
    font-size: 1.2rem;
    color: var(--primary-color);
}

.info-group {
    background-color: #f9fafb;
    padding: 15px;
    border-radius: var(--border-radius);
}

.info-group p {
    margin-bottom: 10px;
}

.order-items, .update-status {
    grid-column: 1 / -1;
    margin-top: 20px;
}

.detail-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.detail-table th,
.detail-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.text-right {
    text-align: right;
}

/* Activity stats */
.activity-stats {
    display: flex;
    gap: 20px;
    margin-top: 15px;
}

.stat {
    background-color: #f9fafb;
    padding: 15px;
    border-radius: var(--border-radius);
    flex: 1;
    text-align: center;
    transition: all var(--transition-speed);
}

.stat:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-shadow);
}

.stat-value {
    display: block;
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--primary-color);
}

.stat-label {
    display: block;
    margin-top: 5px;
    color: var(--medium-gray);
}

/* Date group in forms */
.date-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.product-selector {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    padding: 10px;
    margin-top: 10px;
}

.select-all-group {
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.product-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

/* Status badges */
.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Responsive adjustments for modals and tables */
@media (max-width: 768px) {
    .order-details {
        grid-template-columns: 1fr;
    }
    
    .data-table {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .data-table table {
        min-width: 800px;
    }

    .content-actions {
        flex-direction: column;
        align-items: stretch;
        padding: 15px;
    }

    .search-bar {
        max-width: none;
        margin-bottom: 10px;
    }

    .filter-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .filter-actions select {
        margin-bottom: 10px;
        min-width: 100px;
    }
    
    .modal-content {
        width: 95%;
        max-height: 90vh;
        overflow-y: auto;
        margin: 20px auto;
    }

    .form-row {
        flex-direction: column;
    }

    .form-row .form-group {
        margin-bottom: 15px;
    }
    
    .sidebar {
        width: 70px;
        overflow: hidden;
    }
    
    .sidebar-header h1,
    .sidebar-header p,
    .sidebar nav ul li a span {
        display: none;
    }
    
    .sidebar nav ul li a {
        justify-content: center;
        padding: 15px 0;
    }
    
    .sidebar nav ul li a i {
        margin-right: 0;
        font-size: 1.2rem;
    }
    
    .content {
        margin-left: 70px;
    }
    
    .dashboard-stats {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .dashboard-charts {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .dashboard-stats {
        grid-template-columns: 1fr;
    }
    
    .content {
        padding: 15px;
    }
    
    .content-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .admin-user {
        margin-top: 10px;
    }
    
    .search-box input {
        width: 200px;
    }
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 20px;
    padding: 20px;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
}

.pagination button {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background-color: white;
    color: var(--text-color);
    border-radius: 4px;
    cursor: pointer;
    transition: all var(--transition-speed);
}

.pagination button:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination #page-numbers {
    display: flex;
    gap: 5px;
}

.pagination .page-number {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background-color: white;
    color: var(--text-color);
    border-radius: 4px;
    cursor: pointer;
    transition: all var(--transition-speed);
}

.pagination .page-number.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination .page-number:hover:not(.active) {
    background-color: #f5f5f5;
}

/* Brand specific styles */
.brand-logo {
    width: 40px;
    height: 40px;
    object-fit: contain;
    border-radius: 4px;
    border: 1px solid #e1e5eb;
    background: #f8f9fa;
}

.badge {
    background-color: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Prevent flickering */
.data-table-container {
    min-height: 400px;
    transition: none;
}

.data-table tbody {
    transition: none;
}

/* Smooth loading */
.content {
    opacity: 1;
    transition: opacity 0.3s ease;
}

.content.loading {
    opacity: 0.7;
}

/* Modal improvements */
.modal {
    transition: opacity 0.3s ease;
}

.modal.active {
    display: flex !important;
    opacity: 1;
}

/* Animation */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
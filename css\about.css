/* About Us Page Styles */

.about-section {
    padding: 60px 0;
}

/* About Header */
.about-header {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-bottom: 60px;
    align-items: center;
}

.about-image {
    height: 400px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.about-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.about-intro h2 {
    font-size: 32px;
    margin-bottom: 20px;
    color: var(--dark-color);
}

.about-intro p {
    margin-bottom: 20px;
    line-height: 1.8;
    color: #555;
}

/* Values Section */
.about-values {
    margin-bottom: 60px;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.value-card {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
    text-align: center;
    transition: transform 0.3s, box-shadow 0.3s;
}

.value-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.value-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 28px;
}

.value-card h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: var(--dark-color);
}

.value-card p {
    color: #666;
    line-height: 1.6;
}

/* Story Section */
.about-story {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-bottom: 60px;
    align-items: center;
}

.story-content h2 {
    font-size: 28px;
    margin-bottom: 20px;
    color: var(--dark-color);
}

.story-content p {
    margin-bottom: 20px;
    line-height: 1.8;
    color: #555;
}

.story-image {
    height: 400px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.story-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Team Section */
.about-team {
    margin-bottom: 60px;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.team-member {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s, box-shadow 0.3s;
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.member-image {
    height: 250px;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.team-member h3 {
    font-size: 18px;
    margin: 20px 20px 5px;
    color: var(--dark-color);
}

.member-position {
    color: var(--primary-color);
    font-weight: 500;
    margin: 0 20px 10px;
}

.member-bio {
    color: #666;
    line-height: 1.6;
    margin: 0 20px 20px;
    font-size: 14px;
}

/* Testimonials Section */
.testimonials {
    margin-bottom: 60px;
    position: relative;
}

.testimonials-carousel {
    display: flex;
    overflow: hidden;
    position: relative;
    height: 250px;
    margin-top: 40px;
}

.testimonial {
    min-width: 100%;
    padding: 30px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    position: absolute;
    transition: transform 0.5s ease;
}

.testimonial:nth-child(1) {
    transform: translateX(0);
}

.testimonial:nth-child(2) {
    transform: translateX(100%);
}

.testimonial-content {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
    margin-bottom: 20px;
    position: relative;
}

.testimonial-content:after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 30px;
    width: 20px;
    height: 20px;
    background-color: white;
    transform: rotate(45deg);
    box-shadow: 3px 3px 5px rgba(0, 0, 0, 0.08);
}

.testimonial-content p {
    color: #555;
    line-height: 1.8;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    margin-left: 30px;
}

.author-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
}

.author-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.author-info h4 {
    font-size: 16px;
    margin-bottom: 5px;
    color: var(--dark-color);
}

.author-info p {
    font-size: 14px;
    color: #666;
}

.testimonial-controls {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.testimonial-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: white;
    border: 1px solid #ddd;
    color: var(--dark-color);
    margin: 0 10px;
    cursor: pointer;
    transition: all 0.3s;
}

.testimonial-btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* CTA Section */
.cta-section {
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('../images/placeholder.svg');
    background-size: cover;
    background-position: center;
    color: white;
    padding: 80px 0;
    text-align: center;
}

.cta-content h2 {
    font-size: 32px;
    margin-bottom: 15px;
}

.cta-content p {
    font-size: 18px;
    margin-bottom: 30px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.cta-content .btn {
    margin: 0 10px;
}

/* Responsive Design */
@media (max-width: 992px) {
    .about-header, .about-story {
        grid-template-columns: 1fr;
    }
    
    .about-image, .story-image {
        height: 300px;
        order: 1;
    }
    
    .about-intro, .story-content {
        order: 2;
    }
    
    .testimonials-carousel {
        height: 300px;
    }
}

@media (max-width: 768px) {
    .value-card, .team-member {
        max-width: 320px;
        margin: 0 auto;
    }
    
    .values-grid, .team-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .testimonials-carousel {
        height: 350px;
    }
    
    .cta-content .btn {
        display: block;
        margin: 10px auto;
        max-width: 200px;
    }
} 
document.addEventListener('DOMContentLoaded', () => {
    // Global variables
    window.API_URL = 'http://localhost:3000/api';
    window.ADMIN_VALIDATE_URL = `${window.API_URL}/users/validate-admin`;
    
    // Logout functionality is now handled in login.js
    
    // Notification system
    window.showNotification = function(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <span>${message}</span>
            <button class="close-notification"><i class="fas fa-times"></i></button>
        `;
        
        document.body.appendChild(notification);
        
        // Show notification with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // Auto-hide after 5 seconds
        const hideTimeout = setTimeout(() => {
            hideNotification(notification);
        }, 5000);
        
        // Close button
        const closeBtn = notification.querySelector('.close-notification');
        closeBtn.addEventListener('click', () => {
            clearTimeout(hideTimeout);
            hideNotification(notification);
        });
    };
    
    function hideNotification(notification) {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }
});

// API utility functions
async function apiRequest(endpoint, method = 'GET', data = null) {
    const token = localStorage.getItem('adminToken') || sessionStorage.getItem('adminToken');
    
    const options = {
        method,
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        }
    };
    
    if (data && (method === 'POST' || method === 'PUT')) {
        options.body = JSON.stringify(data);
    }
    
    try {
        const response = await fetch(`${window.API_URL}${endpoint}`, options);
        
        if (response.status === 401) {
            // Redirect to login page
            localStorage.removeItem('adminToken');
            sessionStorage.removeItem('adminToken');
            window.location.href = 'login.html';
            return null;
        }
        
        return await response.json();
    } catch (error) {
        console.error(`API Error (${endpoint}):`, error);
        window.showNotification('An error occurred while communicating with the server.', 'error');
        return null;
    }
}

// Format utilities
function formatDate(dateString) {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
}

function formatCurrency(amount) {
    return '$' + parseFloat(amount).toFixed(2);
}

// Common data loading functions
async function loadProductCount() {
    const productCountElement = document.getElementById('product-count');
    if (!productCountElement) return;
    
    const stats = await apiRequest('/admin/stats');
    if (stats) {
        productCountElement.textContent = stats.productCount || 0;
    }
}

async function loadUserCount() {
    const userCountElement = document.getElementById('user-count');
    if (!userCountElement) return;
    
    const stats = await apiRequest('/admin/stats');
    if (stats) {
        userCountElement.textContent = stats.userCount || 0;
    }
}

async function loadOrderCount() {
    const orderCountElement = document.getElementById('order-count');
    if (!orderCountElement) return;
    
    const stats = await apiRequest('/admin/stats');
    if (stats) {
        orderCountElement.textContent = stats.orderCount || 0;
    }
}

async function loadRevenue() {
    const revenueElement = document.getElementById('revenue');
    if (!revenueElement) return;
    
    const stats = await apiRequest('/admin/stats');
    if (stats) {
        revenueElement.textContent = formatCurrency(stats.totalRevenue || 0);
    }
} 
<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="300" height="600" viewBox="0 0 300 600">
  <!-- Google Pixel Background -->
  <defs>
    <linearGradient id="pixel-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ECEFF1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#CFD8DC;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="screen-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#CDDC39;stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:#8BC34A;stop-opacity:0.2" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="5" />
      <feOffset dx="0" dy="0" />
      <feComposite in2="SourceGraphic" operator="out" />
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0" />
      <feBlend in="SourceGraphic" in2="blurOut" mode="normal" />
    </filter>
  </defs>

  <!-- Phone Body -->
  <rect x="60" y="20" width="180" height="560" rx="20" ry="20" fill="url(#pixel-gradient)" filter="url(#shadow)" />
  
  <!-- Two-tone back design (signature Pixel look) -->
  <rect x="60" y="20" width="180" height="120" rx="20" ry="20" fill="#F5F5F5" />
  <rect x="60" y="129" width="180" height="451" rx="0" ry="0" fill="#E0E0E0" />
  <rect x="60" y="560" width="180" height="20" rx="0" ry="20" fill="#E0E0E0" />
  
  <!-- Screen -->
  <rect x="75" y="75" width="150" height="450" rx="5" ry="5" fill="#FFFFFF" />
  
  <!-- Front Camera -->
  <circle cx="150" cy="45" r="5" fill="#111" />
  
  <!-- Volume Buttons -->
  <rect x="50" y="120" width="5" height="50" rx="2" ry="2" fill="#BDBDBD" />
  
  <!-- Power Button -->
  <rect x="245" y="130" width="5" height="30" rx="2" ry="2" fill="#FF5722" />
  
  <!-- Android Interface -->
  <!-- Status Bar -->
  <rect x="75" y="75" width="150" height="25" fill="#424242" />
  <circle cx="95" cy="87" r="6" fill="#4CAF50" />
  <rect x="110" y="84" width="20" height="7" rx="3" ry="3" fill="#FFFFFF" opacity="0.8" />
  <rect x="190" y="81" width="8" height="12" rx="1" ry="1" fill="#FFFFFF" opacity="0.8" />
  <rect x="205" y="84" width="10" height="7" rx="1" ry="1" fill="#FFFFFF" opacity="0.8" />
  
  <!-- Google Pixel Wallpaper -->
  <rect x="75" y="100" width="150" height="350" fill="url(#screen-gradient)" />
  
  <!-- Abstract shapes in wallpaper -->
  <circle cx="150" cy="250" r="80" fill="#3F51B5" opacity="0.7" />
  <rect x="120" y="150" width="100" height="100" rx="10" ry="10" fill="#4CAF50" opacity="0.5" transform="rotate(45, 170, 200)" />
  <path d="M75,350 L225,350 L150,450 Z" fill="#FF5722" opacity="0.6" />
  
  <!-- App Icons -->
  <g transform="translate(95, 120)">
    <!-- Google Apps -->
    <circle cx="15" cy="15" r="13" fill="#FFFFFF" stroke="#EEEEEE" stroke-width="1" />
    <path d="M7,15 L15,8 L23,15 L19,15 L15,11 L11,15 Z" fill="#4285F4" />
    <path d="M7,15 L15,22 L23,15 L19,15 L15,19 L11,15 Z" fill="#EA4335" />
    <path d="M7,15 L15,22 L7,15 Z" fill="#FBBC05" />
    <path d="M7,15 L15,8 L7,15 Z" fill="#34A853" />
    
    <circle cx="55" cy="15" r="13" fill="#FFFFFF" stroke="#EEEEEE" stroke-width="1" />
    <circle cx="55" cy="15" r="10" fill="#4285F4" />
    <rect x="50" y="12" width="10" height="6" rx="3" ry="3" fill="#FFFFFF" />
    <rect x="52" y="10" width="6" height="2" rx="1" ry="1" fill="#FFFFFF" />
    
    <circle cx="95" cy="15" r="13" fill="#FFFFFF" stroke="#EEEEEE" stroke-width="1" />
    <rect x="89" y="9" width="12" height="12" rx="2" ry="2" fill="#34A853" />
    <path d="M97,15 L93,19 L91,17 L92,16 L93,17 L96,14 Z" fill="#FFFFFF" />
  </g>
  
  <!-- Navigation Bar -->
  <rect x="75" y="450" width="150" height="75" fill="#FFFFFF" />
  <rect x="107" y="482" width="25" height="10" rx="5" ry="5" fill="#212121" />
  <circle cx="150" cy="487" r="15" stroke="#212121" stroke-width="2" fill="none" />
  <polygon points="180,480 188,487 180,494" stroke="#212121" stroke-width="2" fill="none" />
</svg> 
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - RaoufStore</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/login.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <a href="index.html">
                    <i class="fas fa-mobile-alt"></i>
                    <span>RaoufStore</span>
                </a>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="products.html">Products</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html">Contact</a></li>
                </ul>
            </nav>
            <div class="header-actions">
                <a href="cart.html" class="cart-icon">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count">0</span>
                </a>
                <a href="login.html" class="login-btn active">Login</a>
            </div>
        </div>
    </header>

    <section class="page-header">
        <div class="container">
            <h1>Account</h1>
            <div class="breadcrumb">
                <a href="index.html">Home</a> / <span>Login</span>
            </div>
        </div>
    </section>

    <section class="login-section">
        <div class="container">
            <div class="auth-container">
                <div class="auth-tabs">
                    <button class="auth-tab active" data-tab="login-tab">Login</button>
                    <button class="auth-tab" data-tab="register-tab">Register</button>
                </div>
                
                <div class="auth-content">
                    <div class="auth-tab-content active" id="login-tab">
                        <h2>Sign In to Your Account</h2>
                        <form id="login-form">
                            <div class="form-group">
                                <label for="login-email">Email Address</label>
                                <input type="email" id="login-email" name="email" autocomplete="username" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="login-password">Password</label>
                                <div class="password-input">
                                    <input type="password" id="login-password" name="password" autocomplete="current-password" required>
                                    <button type="button" class="toggle-password">
                                        <i class="far fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="form-options">
                                <div class="remember-me">
                                    <input type="checkbox" id="remember-me" name="remember">
                                    <label for="remember-me">Remember me</label>
                                </div>
                                <a href="#" class="forgot-password">Forgot Password?</a>
                            </div>
                            
                            <button type="submit" class="btn primary-btn">Login</button>
                        </form>
                        
                        <div class="social-login">
                            <p>Or login with</p>
                            <div class="social-buttons">
                                <button class="social-btn google-btn">
                                    <i class="fab fa-google"></i>
                                    Google
                                </button>
                                <button class="social-btn facebook-btn">
                                    <i class="fab fa-facebook-f"></i>
                                    Facebook
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="auth-tab-content" id="register-tab">
                        <h2>Create an Account</h2>
                        <form id="register-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="register-first-name">First Name</label>
                                    <input type="text" id="register-first-name" name="firstName" required>
                                </div>
                                <div class="form-group">
                                    <label for="register-last-name">Last Name</label>
                                    <input type="text" id="register-last-name" name="lastName" required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="register-email">Email Address</label>
                                <input type="email" id="register-email" name="email" autocomplete="username" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="register-phone">Phone Number</label>
                                <input type="tel" id="register-phone" name="phone">
                            </div>
                            
                            <div class="form-group">
                                <label for="register-password">Password</label>
                                <div class="password-input">
                                    <input type="password" id="register-password" name="password" autocomplete="new-password" required>
                                    <button type="button" class="toggle-password">
                                        <i class="far fa-eye"></i>
                                    </button>
                                </div>
                                <div class="password-strength">
                                    <div class="strength-meter"></div>
                                    <span class="strength-text">Password strength</span>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="register-confirm-password">Confirm Password</label>
                                <div class="password-input">
                                    <input type="password" id="register-confirm-password" name="confirmPassword" autocomplete="new-password" required>
                                    <button type="button" class="toggle-password">
                                        <i class="far fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="form-group checkbox-group">
                                <input type="checkbox" id="terms" name="terms" required>
                                <label for="terms">I agree to the <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a></label>
                            </div>
                            
                            <button type="submit" class="btn primary-btn">Create Account</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-column">
                    <h3>RaoufStore</h3>
                    <p>Your one-stop shop for the latest smartphones and accessories.</p>
                </div>
                <div class="footer-column">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="products.html">Products</a></li>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="contact.html">Contact Us</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Contact Us</h3>
                    <p>123 Main Street, Tech City, TC 12345</p>
                    <p>Email: <EMAIL></p>
                    <p>Phone: (*************</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 RaoufStore. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/data-manager.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/app.js"></script>
    <script src="js/login.js"></script>
</body>
</html> 
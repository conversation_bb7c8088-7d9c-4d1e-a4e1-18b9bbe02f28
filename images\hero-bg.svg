<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="1920" height="800" viewBox="0 0 1920 800">
  <!-- Background Pattern -->
  <defs>
    <linearGradient id="hero-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#303F9F;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5C6BC0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="phone-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:0.05" />
      <stop offset="100%" style="stop-color:#FFFFFF;stop-opacity:0.2" />
    </linearGradient>
    <pattern id="hero-dots" width="20" height="20" patternUnits="userSpaceOnUse">
      <circle cx="10" cy="10" r="1" fill="#FFFFFF" opacity="0.2" />
    </pattern>
  </defs>
  
  <!-- Main Background -->
  <rect width="1920" height="800" fill="url(#hero-gradient)" />
  <rect width="1920" height="800" fill="url(#hero-dots)" />
  
  <!-- Abstract Wave Pattern -->
  <path d="M0,500 C400,600 800,450 1200,500 C1600,550 1920,500 1920,500 L1920,800 L0,800 Z" fill="#FFFFFF" opacity="0.05" />
  <path d="M0,550 C500,650 1000,500 1500,550 C1800,580 1920,550 1920,550 L1920,800 L0,800 Z" fill="#FFFFFF" opacity="0.08" />
  <path d="M0,600 C600,700 1200,600 1800,650 L1920,650 L1920,800 L0,800 Z" fill="#FFFFFF" opacity="0.1" />
  
  <!-- Smartphone Outlines -->
  <!-- Phone 1 -->
  <g transform="translate(1600, 400) rotate(15)">
    <rect x="-80" y="-160" width="160" height="320" rx="20" ry="20" stroke="#FFFFFF" stroke-width="2" fill="url(#phone-gradient)" />
    <rect x="-70" y="-140" width="140" height="280" rx="10" ry="10" stroke="#FFFFFF" stroke-width="1" fill="none" opacity="0.8" />
    <circle cx="0" cy="120" r="15" stroke="#FFFFFF" stroke-width="1" fill="none" />
  </g>
  
  <!-- Phone 2 -->
  <g transform="translate(1400, 450) rotate(-10)">
    <rect x="-70" y="-140" width="140" height="280" rx="18" ry="18" stroke="#FFFFFF" stroke-width="2" fill="url(#phone-gradient)" />
    <rect x="-60" y="-125" width="120" height="250" rx="8" ry="8" stroke="#FFFFFF" stroke-width="1" fill="none" opacity="0.8" />
    <circle cx="0" cy="105" r="12" stroke="#FFFFFF" stroke-width="1" fill="none" />
  </g>
  
  <!-- Phone 3 -->
  <g transform="translate(1700, 500) rotate(5)">
    <rect x="-65" y="-130" width="130" height="260" rx="15" ry="15" stroke="#FFFFFF" stroke-width="2" fill="url(#phone-gradient)" />
    <rect x="-55" y="-115" width="110" height="230" rx="8" ry="8" stroke="#FFFFFF" stroke-width="1" fill="none" opacity="0.8" />
    <circle cx="0" cy="95" r="12" stroke="#FFFFFF" stroke-width="1" fill="none" />
  </g>
  
  <!-- Animated Particles (Circles) -->
  <circle cx="200" cy="200" r="5" fill="#FFFFFF" opacity="0.6" />
  <circle cx="500" cy="150" r="3" fill="#FFFFFF" opacity="0.5" />
  <circle cx="800" cy="250" r="4" fill="#FFFFFF" opacity="0.7" />
  <circle cx="1100" cy="180" r="6" fill="#FFFFFF" opacity="0.4" />
  <circle cx="1400" cy="220" r="3" fill="#FFFFFF" opacity="0.6" />
  <circle cx="1700" cy="150" r="5" fill="#FFFFFF" opacity="0.5" />
  <circle cx="300" cy="300" r="4" fill="#FFFFFF" opacity="0.6" />
  <circle cx="600" cy="350" r="6" fill="#FFFFFF" opacity="0.5" />
  <circle cx="900" cy="300" r="3" fill="#FFFFFF" opacity="0.7" />
  <circle cx="1200" cy="250" r="5" fill="#FFFFFF" opacity="0.4" />
  <circle cx="1500" cy="350" r="4" fill="#FFFFFF" opacity="0.6" />
  <circle cx="1800" cy="300" r="3" fill="#FFFFFF" opacity="0.5" />
  
  <!-- Floating Device Icons -->
  <g transform="translate(250, 250)" opacity="0.7">
    <rect x="-20" y="-30" width="40" height="60" rx="5" ry="5" stroke="#FFFFFF" stroke-width="2" fill="none" />
    <line x1="-10" y1="-20" x2="10" y2="-20" stroke="#FFFFFF" stroke-width="2" />
    <line x1="-10" y1="-10" x2="10" y2="-10" stroke="#FFFFFF" stroke-width="2" />
    <line x1="-10" y1="0" x2="10" y2="0" stroke="#FFFFFF" stroke-width="2" />
    <circle cx="0" cy="20" r="5" stroke="#FFFFFF" stroke-width="2" fill="none" />
  </g>
  
  <g transform="translate(1100, 400)" opacity="0.7">
    <rect x="-25" y="-15" width="50" height="30" rx="3" ry="3" stroke="#FFFFFF" stroke-width="2" fill="none" />
    <rect x="-22" y="-12" width="44" height="24" rx="2" ry="2" stroke="#FFFFFF" stroke-width="1" fill="none" />
    <rect x="-15" y="15" width="30" height="3" stroke="#FFFFFF" stroke-width="1" fill="#FFFFFF" />
  </g>
  
  <g transform="translate(600, 200)" opacity="0.7">
    <circle cx="0" cy="0" r="20" stroke="#FFFFFF" stroke-width="2" fill="none" />
    <circle cx="0" cy="0" r="15" stroke="#FFFFFF" stroke-width="1" fill="none" />
    <circle cx="0" cy="0" r="3" stroke="#FFFFFF" stroke-width="1" fill="#FFFFFF" />
    <line x1="-15" y1="0" x2="-5" y2="0" stroke="#FFFFFF" stroke-width="1" />
    <line x1="5" y1="0" x2="15" y2="0" stroke="#FFFFFF" stroke-width="1" />
    <line x1="0" y1="-15" x2="0" y2="-5" stroke="#FFFFFF" stroke-width="1" />
    <line x1="0" y1="5" x2="0" y2="15" stroke="#FFFFFF" stroke-width="1" />
  </g>
</svg> 